<?php

namespace Xmetr\RealEstate\Http\Requests;

use Xmetr\RealEstate\Enums\PropertyStatusEnum;
use Xmetr\RealEstate\Http\Requests\PropertyRequest as BaseRequest;
use Illuminate\Validation\Rule;

class AccountPropertyRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:220',
            'description' => 'nullable|string|max:350',
            'original_description' => 'nullable|string',
            'content' => 'nullable|string',
            'number_bedroom' => 'numeric|min:0|max:100000|nullable',
            'number_bathroom' => 'numeric|min:0|max:100000|nullable',
            'number_floor' => 'numeric|min:0|max:100000|nullable',
            'price' => 'numeric|min:0|nullable',
            'status' => Rule::in(PropertyStatusEnum::values()),
            'latitude' => ['max:20', 'nullable', 'regex:/^[-]?(([0-8]?[0-9])\.(\d+))|(90(\.0+)?)$/'],
            'longitude' => [
                'max:20',
                'nullable',
                'regex:/^[-]?((((1[0-7][0-9])|([0-9]?[0-9]))\.(\d+))|180(\.0+)?)$/',
            ],
            'private_notes' => ['nullable', 'string', 'max:10000'],
        ];
    }
}
