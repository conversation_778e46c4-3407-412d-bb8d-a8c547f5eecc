<?php

namespace Xmetr\RealEstate\Repositories\Eloquent;

use Xmetr\Base\Models\BaseQueryBuilder;
use Xmetr\Language\Facades\Language;
use Xmetr\RealEstate\Enums\ModerationStatusEnum;
use Xmetr\RealEstate\Enums\PropertyStatusEnum;
use Xmetr\RealEstate\Enums\PropertyTypeEnum;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface;
use Xmetr\Support\Repositories\Eloquent\RepositoriesAbstract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class PropertyRepository extends RepositoriesAbstract implements PropertyInterface
{
    public function getRelatedProperties(int $propertyId, int $limit = 4, array $with = [], array $extra = []): Collection|LengthAwarePaginator
    {
        $limit = $limit > 1 ? $limit : 4;
        $currentProperty = $this->findById($propertyId, ['categories']);

        $this->model = $this->originalModel;

        // @phpstan-ignore-next-line
        $this->model = $this->model
            ->where('id', '<>', $propertyId)
            ->active();

        if ($currentProperty && $currentProperty->categories->count()) {
            $percentage = 0.3; // 30%
            $priceMin = $currentProperty->price * (1 - $percentage);
            $priceMax = $currentProperty->price * (1 + $percentage);

            $categoryIds = $currentProperty->categories->pluck('id')->toArray();

            $this->model
                ->whereHas('categories', function ($query) use ($categoryIds): void {
                    $query->whereIn('category_id', $categoryIds);
                })
                ->where('type', $currentProperty->type)
                ->where('city_id', $currentProperty->city_id)
                ->whereBetween('price', [$priceMin, $priceMax])
                ->inRandomOrder()
                ;
        }

        $params = array_merge([
            'condition' => [],
            'order_by' => [
                'created_at' => 'DESC',
            ],
            'take' => $limit,
            'with' => $with,
        ], $extra);

        return $this->advancedGet($params);
    }

    public function getProperties(array $filters = [], array $params = []): Collection|LengthAwarePaginator
    {
        $filters = array_merge([
            'keyword' => null,
            'type' => null,
            'bedroom' => null,
            'bathroom' => null,
            'floor' => null,
            'min_square' => null,
            'max_square' => null,
            'min_price' => null,
            'max_price' => null,
            'project' => null,
            'project_id' => null,
            'category_id' => null,
            'author_id' => null,
            'account_type' => null,
            'bills_included' => null,
            'furnished' => null,
            'pets_allowed' => null,
            'smoking_allowed' => null,
            'online_view_tour' => null,
            'country_id' => null,
            'city_id' => null,
            'country' => null,
            'city' => null,
            'state' => null,
            'state_id' => null,
            'location' => null,
            'sort_by' => null,
            'features' => null,
            'suitable' => null,
            'spoken_languages' => null,
            'required_documents' => null,
        ], $filters);

        $orderBy = match ($filters['sort_by']) {
            'date_asc' => [
                'created_at' => 'ASC',
            ],
            'price_asc' => [
                'price' => 'ASC',
            ],
            'price_desc' => [
                'price' => 'DESC',
            ],
            'name_asc' => [
                'name' => 'ASC',
            ],
            'name_desc' => [
                'name' => 'DESC',
            ],
            default => [
                'created_at' => 'DESC',
            ],
        };

        $params = array_merge([
            'condition' => [],
            'order_by' => [
                'created_at' => 'DESC',
            ],
            'take' => null,
            'paginate' => [
                'per_page' => 10,
                'current_paged' => 1,
            ],
            'select' => [
                '*',
            ],
            'with' => [],
        ], $params);

        $params['order_by'] = $orderBy;

        // @phpstan-ignore-next-line
        $this->model = $this->originalModel->active();

        if ($filters['keyword'] !== null) {
            $keyword = $filters['keyword'];

            if (is_plugin_active('language') && is_plugin_active('language-advanced') && Language::getCurrentLocale() != Language::getDefaultLocale()) {
                $this->model = $this->model
                    ->whereHas('translations', function (BaseQueryBuilder $query) use ($keyword): void {
                        $query
                            ->addSearch('name', $keyword, false, false)
                            ->addSearch('location', $keyword, false)
                            ->addSearch('description', $keyword, false)
                            ->addSearch('unique_id', $keyword, false);
                    });
            } else {
                $this->model = $this->model
                    ->where(function (BaseQueryBuilder $query) use ($keyword) {
                        return $query
                            ->addSearch('name', $keyword, false, false)
                            ->addSearch('location', $keyword, false)
                            ->addSearch('description', $keyword, false)
                            ->addSearch('unique_id', $keyword, false);
                    });
            }
        }

        $this->model = $this->model->where('status', '!=', PropertyStatusEnum::RENTED);

        if ($filters['type'] !== null) {
            if ($filters['type'] == PropertyTypeEnum::SALE) {
                $this->model = $this->model->where('type', $filters['type']);
            } else {
                $this->model = $this->model->where('type', $filters['type']);
            }
        }

        if ($filters['bedroom']) {
            if ($filters['bedroom'] < 5) {
                $this->model = $this->model->where('number_bedroom', $filters['bedroom']);
            } else {
                $this->model = $this->model->where('number_bedroom', '>=', $filters['bedroom']);
            }
        }

        if ($filters['bathroom']) {
            if ($filters['bathroom'] < 5) {
                $this->model = $this->model->where('number_bathroom', $filters['bathroom']);
            } else {
                $this->model = $this->model->where('number_bathroom', '>=', $filters['bathroom']);
            }
        }

        if ($filters['floor']) {
            if ($filters['floor'] < 5) {
                $this->model = $this->model->where('number_floor', $filters['floor']);
            } else {
                $this->model = $this->model->where('number_floor', '>=', $filters['floor']);
            }
        }

        if ($filters['min_square'] !== null || $filters['max_square'] !== null) {
            $this->model = $this->model
                ->where(function (Builder $query) use ($filters) {
                    $minSquare = Arr::get($filters, 'min_square');
                    $maxSquare = Arr::get($filters, 'max_square');

                    if ($minSquare !== null) {
                        $query = $query->where('square', '>=', $minSquare);
                    }

                    if ($maxSquare !== null) {
                        $query = $query->where('square', '<=', $maxSquare);
                    }

                    return $query;
                });
        }

        if ($filters['min_price'] !== null || $filters['max_price'] !== null) {
            $this->model = $this->model
                ->where(function (Builder $query) use ($filters) {
                    $minPrice = Arr::get($filters, 'min_price');
                    $maxPrice = Arr::get($filters, 'max_price');

                    if ($minPrice !== null) {
                        $query = $query->where('price', '>=', $minPrice);
                    }

                    if ($maxPrice !== null) {
                        $query = $query->where('price', '<=', $maxPrice);
                    }

                    return $query;
                });
        }

        if ($filters['city'] !== null) {
            $this->model = $this->model->whereHas('city', function ($query) use ($filters): void {
                $query->where('slug', $filters['city']);
            });
        }

        if ($filters['state'] !== null) {
            $this->model = $this->model->whereHas('state', function ($query) use ($filters): void {
                $query->where('slug', $filters['state']);
            });
        }

        if ($filters['project'] !== null) {
            $this->model = $this->model->where(function (BaseQueryBuilder $query) use ($filters): void {
                $query
                    ->where('project_id', $filters['project'])
                    ->orWhereHas('project', function (BaseQueryBuilder $query) use ($filters): void {
                        $query->addSearch('re_projects.name', $filters['project'], false, false);
                    });
            });
        }

        if ($filters['project_id'] !== null) {
            $this->model = $this->model->where('project_id', $filters['project_id']);
        }

        if ($filters['author_id'] !== null) {
            $this->model = $this->model
                ->where('author_id', $filters['author_id'])
                ->where('author_type', Account::class);
        }

        if ($filters['account_type'] !== null) {
            $this->model = $this->model
                ->where('author_type', Account::class)
                ->whereHas('author', function ($query) use ($filters): void {
                    $query->where('account_type', $filters['account_type']);
                });
        }

        if ($filters['bills_included'] !== null) {
            $this->model = $this->model->where('bills_included', (bool) $filters['bills_included']);
        }

        if ($filters['furnished'] !== null) {
            $this->model = $this->model->where('furnished', (bool) $filters['furnished']);
        }

        if ($filters['pets_allowed'] !== null) {
            $this->model = $this->model->where('pets_allowed', (bool) $filters['pets_allowed']);
        }

        if ($filters['smoking_allowed'] !== null) {
            $this->model = $this->model->where('smoking_allowed', (bool) $filters['smoking_allowed']);
        }

        if ($filters['online_view_tour'] !== null) {
            $this->model = $this->model->where('online_view_tour', (bool) $filters['online_view_tour']);
        }

        if ($filters['category_id'] !== null) {
            $categoryIds = get_property_categories_related_ids($filters['category_id']);
            $this->model = $this->model
                ->whereHas('categories', function ($query) use ($categoryIds): void {
                    $query->whereIn('re_categories.id', $categoryIds);
                });
        }

        if ($filters['type'] !== null) {
            $categoryIds = get_property_categories_related_ids_by_slug($filters['type']);
            if ($categoryIds) {
                $this->model = $this->model
                    ->whereHas('categories', function ($query) use ($categoryIds): void {
                        $query->whereIn('re_categories.id', $categoryIds);
                    });
            }
        }

        if ($filters['state_id']) {
            $this->model = $this->model->where('state_id', $filters['state_id']);
        }

        if ($filters['country_id']) {
            $this->model = $this->model->where('country_id', $filters['country_id']);
        }
        if ($filters['city_id']) {
            $this->model = $this->model->where('city_id', $filters['city_id']);
        } elseif ($filters['location']) {
            $locationData = explode(',', $filters['location']);

            if (count($locationData) > 1) {
                $locationSearch = trim($locationData[0]);
            } else {
                $locationSearch = trim($filters['location']);
            }

            if (is_plugin_active('language') && is_plugin_active('language-advanced') && Language::getCurrentLocale() != Language::getDefaultLocale()) {
                $this->model = $this->model
                    ->where(function (BaseQueryBuilder $query) use ($locationSearch) {
                        return $query
                            ->whereHas('translations', function (BaseQueryBuilder $query) use ($locationSearch): void {
                                $query->addSearch('location', $locationSearch, false, false);
                            })
                            ->orWhereHas('city.translations', function (BaseQueryBuilder $query) use ($locationSearch): void {
                                $query->addSearch('name', $locationSearch, false, false);
                            })
                            ->orWhereHas('state.translations', function (BaseQueryBuilder $query) use ($locationSearch): void {
                                $query->addSearch('name', $locationSearch, false, false);
                            });
                    });
            } else {
                $this->model = $this->model
                    ->where(function ($query) use ($locationSearch) {
                        return $query
                            ->addSearch('location', $locationSearch, false, false)
                            ->orWhereHas('city', function (BaseQueryBuilder $query) use ($locationSearch): void {
                                $query->addSearch('cities.name', $locationSearch, false, false);
                            })
                            ->orWhereHas('state', function (BaseQueryBuilder $query) use ($locationSearch): void {
                                $query->addSearch('states.name', $locationSearch, false, false);
                            });
                    });
            }
        }

        if (count($filters['category_ids'] ?? [])) {
            $categoryIds = $filters['category_ids'];

            $this->model = $this->model
                ->whereHas('categories', function (Builder $query) use ($categoryIds): void {
                    $query->whereIn('category_id', $categoryIds);
                });
        }

        if ($filters['locations'] ?? []) {
            $locationsSearch = $filters['locations'];

            if (is_plugin_active('language') && is_plugin_active('language-advanced') && Language::getCurrentLocale() != Language::getDefaultLocale()) {
                $this->model = $this->model
                    ->where(function (BaseQueryBuilder $query) use ($locationsSearch) {
                        return $query
                            ->whereHas('translations', function (BaseQueryBuilder $query) use ($locationsSearch): void {
                                foreach ($locationsSearch as $location) {
                                    $query->addSearch('location', $location, false);
                                }
                            })
                            ->orWhereHas('city.translations', function (BaseQueryBuilder $query) use ($locationsSearch): void {
                                foreach ($locationsSearch as $location) {
                                    $query->addSearch('name', $location, false);
                                }
                            })
                            ->orWhereHas('state.translations', function (BaseQueryBuilder $query) use ($locationsSearch): void {
                                foreach ($locationsSearch as $location) {
                                    $query->addSearch('name', $location, false);
                                }
                            });
                    });
            } else {
                $this->model = $this->model
                    ->where(function (BaseQueryBuilder $query) use ($locationsSearch) {
                        return $query
                            ->where(function (BaseQueryBuilder $query) use ($locationsSearch): void {
                                foreach ($locationsSearch as $location) {
                                    $query->addSearch('location', $location, false);
                                }
                            })
                            ->orWhereHas('city', function (BaseQueryBuilder $query) use ($locationsSearch): void {
                                foreach ($locationsSearch as $location) {
                                    $query->addSearch('cities.name', $location, false);
                                }
                            })
                            ->orWhereHas('state', function (BaseQueryBuilder $query) use ($locationsSearch): void {
                                foreach ($locationsSearch as $location) {
                                    $query->addSearch('states.name', $location, false);
                                }
                            });
                    });
            }
        }

        if ($filters['features'] !== null) {
            $features = array_filter((array) $filters['features']);

            if ($features) {
                $propertyIds = $this
                    ->getModel()
                    ->toBase()
                    ->select('re_properties.id')
                    ->join('re_property_features', 're_properties.id', '=', 're_property_features.property_id')
                    ->whereIn('re_property_features.feature_id', $features)
                    ->groupBy('re_properties.id')
                    ->havingRaw('COUNT(DISTINCT re_property_features.feature_id) = ' . count($features))
                    ->pluck('re_properties.id')
                    ->all();

                $this->model = $this->model->whereIn('id', $propertyIds);
            }
        }

        if ($filters['suitable'] !== null) {
            $suitable = array_filter((array) $filters['suitable']);

            if ($suitable) {
                $propertyIds = $this
                    ->getModel()
                    ->toBase()
                    ->select('re_properties.id')
                    ->join('re_property_suitable', 're_properties.id', '=', 're_property_suitable.property_id')
                    ->whereIn('re_property_suitable.suitable_id', $suitable)
                    ->groupBy('re_properties.id')
                    ->havingRaw('COUNT(DISTINCT re_property_suitable.suitable_id) = ' . count($suitable))
                    ->pluck('re_properties.id')
                    ->all();

                $this->model = $this->model->whereIn('id', $propertyIds);
            }
        }

        if ($filters['spoken_languages'] !== null) {
            $spokenLanguages = array_filter((array) $filters['spoken_languages']);

            if ($spokenLanguages) {
                $this->model = $this->model
                    ->where('author_type', Account::class)
                    ->whereHas('author', function ($query) use ($spokenLanguages): void {
                        $query->whereHas('spokenLanguages', function ($subQuery) use ($spokenLanguages): void {
                            $subQuery->whereIn('re_spoken_languages.id', $spokenLanguages);
                        });
                    });
            }
        }

        if (!empty($filters['required_documents'])) {
            $requiredDocs = is_array($filters['required_documents'])
                ? $filters['required_documents']
                : [$filters['required_documents']];

            foreach ($requiredDocs as $document) {
                $this->model->whereJsonContains('required_documents', $document);
            }
        }

        $this->model = apply_filters('properties_filter_query', $this->model, $filters, $params);

        return $this->advancedGet($params);
    }

    public function getProperty(int $propertyId, array $with = [], array $extra = []): ?Property
    {
        $params = array_merge([
            'condition' => [
                'id' => $propertyId,
                'moderation_status' => ModerationStatusEnum::APPROVED,
            ],
            'with' => $with,
            'take' => 1,
        ], $extra);

        // @phpstan-ignore-next-line
        $this->model = $this->originalModel->notExpired();

        return $this->advancedGet($params);
    }

    public function getPropertiesByConditions(array $condition, int $limit = 4, array $with = []): Collection|LengthAwarePaginator
    {
        $limit = $limit > 1 ? $limit : 4;

        // @phpstan-ignore-next-line
        $this->model = $this->originalModel->active();

        $params = [
            'condition' => $condition,
            'with' => $with,
            'take' => $limit,
            'order_by' => ['created_at' => 'DESC'],
        ];

        return $this->advancedGet($params);
    }
}

