<?php

// Test to check what slugs actually exist in the database
echo "=== Testing Slug Lookup ===\n\n";

// Manually test the slug lookup logic
$testSlug = 'apartment';
echo "Testing slug: '{$testSlug}'\n";

// Simulate what our helper function does
$prefix = 'property-category'; // From the service provider
$model = 'Xmetr\\RealEstate\\Models\\Category';

echo "Looking for slug with:\n";
echo "  Key: '{$testSlug}'\n";
echo "  Prefix: '{$prefix}'\n";
echo "  Model: '{$model}'\n\n";

// This would be the database query that SlugHelper::getSlug performs:
// SELECT * FROM slugs WHERE key = 'apartment' AND prefix = 'property-category' AND reference_type = 'Xmetr\\RealEstate\\Models\\Category'

echo "Expected database query:\n";
echo "SELECT * FROM slugs WHERE key = '{$testSlug}' AND prefix = '{$prefix}' AND reference_type = '{$model}'\n\n";

// Test different possible slug variations
$possibleSlugs = [
    'apartment',
    'Apartment', 
    'apartments',
    'villa',
    'condo',
    'house',
    'land',
    'commercial-property',
    'commercial_property'
];

echo "Possible slug variations to test:\n";
foreach ($possibleSlugs as $slug) {
    echo "  - '{$slug}'\n";
}

echo "\nTo debug this issue:\n";
echo "1. Check the 'slugs' table in the database\n";
echo "2. Look for records where reference_type = '{$model}'\n";
echo "3. Check what the actual 'key' values are\n";
echo "4. Verify the 'prefix' values match '{$prefix}'\n";
echo "5. Check the corresponding categories in 're_categories' table\n";

echo "\nSQL queries to run:\n";
echo "SELECT * FROM slugs WHERE reference_type = '{$model}';\n";
echo "SELECT * FROM re_categories;\n";

echo "\nTest completed!\n";
