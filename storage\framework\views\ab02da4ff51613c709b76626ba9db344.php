<?php
    $icon = Arr::get($formOptions, 'icon');
    $heading = Arr::get($formOptions, 'heading');
    $description = Arr::get($formOptions, 'description');
?>

<?php if(Arr::get($formOptions, 'has_wrapper', 'yes') === 'yes'): ?>
<div class="container">
    <div class="row justify-content-center py-5">
        <div class="col-xl-6 col-lg-8">
        <?php endif; ?>
            <div class="card bg-body-tertiary border-0 auth-card">
                <?php if($banner = Arr::get($formOptions, 'banner')): ?>
                    <?php echo e(RvMedia::image($banner, $heading ?: '', attributes: ['class' => 'card-img-top mb-3'])); ?>

                <?php endif; ?>

                <?php if($icon || $heading || $description): ?>
                    <div class="card-header bg-body-tertiary border-0 p-5 pb-0">
                        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['d-flex flex-column flex-md-row align-items-start gap-3' => $icon, 'text-center' => ! $icon]); ?>">
                            <?php if($icon): ?>
                                <div class="bg-white p-3 rounded">
                                    <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => $icon] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'text-primary']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                                </div>
                            <?php endif; ?>
                            <div>
                                <?php if($heading): ?>
                                    <h3 class="fs-4 mb-1"><?php echo e($heading); ?></h3>
                                <?php endif; ?>
                                <?php if($description): ?>
                                    <p class="text-muted"><?php echo e($description); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="card-body p-5 pt-3">
                    <?php if($showStart): ?>
                        <?php echo Form::open(Arr::except($formOptions, ['template'])); ?>

                    <?php endif; ?>

                    <?php if(session()->has('status')): ?>
                        <div role="alert" class="alert alert-success">
                            <?php echo e(session('status')); ?>

                        </div>
                    <?php elseif(session()->has('auth_error_message')): ?>
                        <div role="alert" class="alert alert-danger">
                            <?php echo e(session('auth_error_message')); ?>

                        </div>
                    <?php elseif(session()->has('auth_success_message')): ?>
                        <div role="alert" class="alert alert-success">
                            <?php echo e(session('auth_success_message')); ?>

                        </div>
                    <?php elseif(session()->has('auth_warning_message')): ?>
                        <div role="alert" class="alert alert-warning">
                            <?php echo e(session('auth_warning_message')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if($showFields): ?>
                        <?php echo e($form->getOpenWrapperFormColumns()); ?>


                        <?php $__currentLoopData = $fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(in_array($field->getName(), $exclude)) continue; ?>

                            <?php echo $field->render(); ?>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <?php echo e($form->getCloseWrapperFormColumns()); ?>

                    <?php endif; ?>

                    <?php if($showEnd): ?>
                        <?php echo Form::close(); ?>

                    <?php endif; ?>

                    <?php if($form->getValidatorClass()): ?>
                        <?php $__env->startPush('footer'); ?>
                            <?php echo $form->renderValidatorJs(); ?>

                        <?php $__env->stopPush(); ?>
                    <?php endif; ?>
                </div>
            </div>
            <?php if(Arr::get($formOptions, 'has_wrapper', 'yes') === 'yes'): ?>
        </div>
    </div>
</div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform/plugins/real-estate/resources/views/forms/auth.blade.php ENDPATH**/ ?>