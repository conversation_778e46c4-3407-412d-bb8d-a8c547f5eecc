<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Checking Category ID 7 ===\n\n";

// Find category with ID 7
$category = \Xmetr\RealEstate\Models\Category::find(7);

if ($category) {
    echo "Category ID 7 found:\n";
    echo "  Name: {$category->name}\n";
    echo "  Status: {$category->status}\n";
    echo "  Created: {$category->created_at}\n";
    echo "\n";
    
    // Find the slug for this category
    $slug = \Xmetr\Slug\Models\Slug::where('reference_type', \Xmetr\RealEstate\Models\Category::class)
        ->where('reference_id', 7)
        ->first();
    
    if ($slug) {
        echo "Slug found for category 7:\n";
        echo "  Key: '{$slug->key}'\n";
        echo "  Prefix: '{$slug->prefix}'\n";
        echo "  Reference ID: {$slug->reference_id}\n";
        echo "\n";
        
        // Test our helper function with this slug
        echo "Testing helper function with slug '{$slug->key}':\n";
        $result = get_property_category_id_by_slug($slug->key);
        echo "  Result: " . ($result ? "Category ID {$result}" : "NULL") . "\n";
        echo "\n";
        
        // Test the URL that should work
        echo "URL that should work: /properties?type={$slug->key}\n";
    } else {
        echo "No slug found for category 7\n";
    }
} else {
    echo "Category ID 7 not found\n";
}

// Also check all category slugs
echo "\n=== All Category Slugs ===\n";
$slugs = \Xmetr\Slug\Models\Slug::where('reference_type', \Xmetr\RealEstate\Models\Category::class)->get();

foreach ($slugs as $slug) {
    $cat = \Xmetr\RealEstate\Models\Category::find($slug->reference_id);
    echo "Slug: '{$slug->key}' -> Category ID: {$slug->reference_id}";
    if ($cat) {
        echo " (Name: '{$cat->name}', Status: '{$cat->status}')";
    }
    echo "\n";
}

echo "\nDone!\n";
