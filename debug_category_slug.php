<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Debugging Category Slug Lookup ===\n\n";

// Test 1: Check what slugs exist for categories
echo "1. Checking existing category slugs:\n";
$slugs = \Xmetr\Slug\Models\Slug::where('reference_type', \Xmetr\RealEstate\Models\Category::class)->get();
foreach ($slugs as $slug) {
    echo "   Slug: '{$slug->key}' -> Category ID: {$slug->reference_id} (prefix: '{$slug->prefix}')\n";
}
echo "\n";

// Test 2: Check categories
echo "2. Checking categories:\n";
$categories = \Xmetr\RealEstate\Models\Category::all();
foreach ($categories as $category) {
    echo "   Category ID: {$category->id} -> Name: '{$category->name}'\n";
}
echo "\n";

// Test 3: Test our helper function with different inputs
echo "3. Testing helper function:\n";
$testSlugs = ['apartment', 'Apartment', 'villa', 'condo', 'house', 'land', 'commercial-property'];

foreach ($testSlugs as $testSlug) {
    echo "   Testing slug: '{$testSlug}'\n";

    // Test the SlugHelper::getSlug method directly
    $prefix = \Xmetr\Slug\Facades\SlugHelper::getPrefix(\Xmetr\RealEstate\Models\Category::class);
    echo "     Prefix: '{$prefix}'\n";

    $slugRecord = \Xmetr\Slug\Facades\SlugHelper::getSlug(
        $testSlug,
        $prefix,
        \Xmetr\RealEstate\Models\Category::class
    );

    if ($slugRecord) {
        echo "     Found slug record: ID {$slugRecord->id}, Key: '{$slugRecord->key}', Reference ID: {$slugRecord->reference_id}\n";

        // Check the category directly
        $category = \Xmetr\RealEstate\Models\Category::find($slugRecord->reference_id);
        if ($category) {
            echo "     Category found: ID {$category->id}, Name: '{$category->name}', Status: '{$category->status}'\n";
        }

        // Test our helper function
        $categoryId = get_property_category_id_by_slug($testSlug);
        echo "     Helper function result: " . ($categoryId ? "Category ID {$categoryId}" : "NULL") . "\n";
    } else {
        echo "     No slug record found\n";
    }
    echo "\n";
}

// Test 4: Test with exact slug from database
echo "4. Testing with exact slugs from database:\n";
foreach ($slugs as $slug) {
    echo "   Testing exact slug: '{$slug->key}'\n";
    $categoryId = get_property_category_id_by_slug($slug->key);
    echo "     Helper function result: " . ($categoryId ? "Category ID {$categoryId}" : "NULL") . "\n";
    echo "\n";
}

echo "Debug completed!\n";
