<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasColumn('re_properties', 'original_description')) {
            Schema::table('re_properties', function (Blueprint $table) {
                $table->longText('original_description')->nullable()->after('description');
            });
        }

        if (! Schema::hasColumn('re_properties_translations', 'original_description')) {
            Schema::table('re_properties_translations', function (Blueprint $table) {
                $table->longText('original_description')->nullable()->after('description');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('re_properties', function (Blueprint $table) {
            $table->dropColumn('original_description');
        });

        Schema::table('re_properties_translations', function (Blueprint $table) {
            $table->dropColumn('original_description');
        });
    }
};
