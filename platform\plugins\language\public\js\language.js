(()=>{function t(a){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(a)}function a(t,a){for(var n=0;n<a.length;n++){var l=a[n];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(t,e(l.key),l)}}function e(a){var e=function(a,e){if("object"!=t(a)||!a)return a;var n=a[Symbol.toPrimitive];if(void 0!==n){var l=n.call(a,e||"default");if("object"!=t(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(a)}(a,"string");return"symbol"==t(e)?e:e+""}var n=function(){function t(){!function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,t)}return e=t,l=[{key:"formatState",value:function(t){return!t.id||t.element.value.toLowerCase().includes("...")?t.text:$('<div>\n                <span class="dropdown-item-indicator">\n                    <img src="'.concat($("#language_flag_path").val()).concat(t.element.value.toLowerCase(),'.svg" class="flag" style="height: 16px;" alt="').concat(t.text,'">\n                </span>\n                <span>').concat(t.text,"</span>\n            </div\n        "))}},{key:"createOrUpdateLanguage",value:function(t,a,e,n,l,r,o,i){var g=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,c=$("#btn-language-submit");g&&(c=g);var u=c.data("store-url");i&&(u=$("#btn-language-submit-edit").data("update-url")+"?lang_code=".concat(n)),Xmetr.showButtonLoading(c,!0),$httpClient.make().post(u,{lang_id:t.toString(),lang_name:a,lang_locale:e,lang_code:n,lang_flag:l,lang_order:r,lang_is_rtl:o}).then((function(a){var e=a.data;i?$(".table-language").find("tr[data-id="+t+"]").replaceWith(e.data):$(".table-language").append(e.data),Xmetr.showSuccess(e.message)})).finally((function(){$("#language_id").val("").trigger("change"),$("#lang_name").val(""),$("#lang_locale").val("").trigger("change"),$("#lang_code").val("").trigger("change"),$('input[name=lang_rtl][value="0"]').prop("checked",!0),$("#flag_list").val("").trigger("change"),$("#btn-language-submit-edit").prop("id","btn-language-submit").text($("#btn-language-submit").data("add-language-text")),Xmetr.hideButtonLoading(c)}))}}],(n=[{key:"init",value:function(){var a=this;Xmetr.select($(".select-search-language"),{templateResult:t.formatState,templateSelection:t.formatState});var e=$(".table-language");$(document).on("change","#language_id",(function(t){var a=$(t.currentTarget).find("option:selected").data("language");void 0!==a&&a.length>0&&($("#lang_name").val(a[2]).trigger("change"),$("#lang_locale").val(a[0]).trigger("change"),$("#lang_code").val(a[1]).trigger("change"),$('input[name=lang_rtl][value="'.concat("rtl"===a[3]?1:0,'"]')).prop("checked",!0),$("#flag_list").val(a[4]).trigger("change"),$("#btn-language-submit-edit").prop("id","btn-language-submit").text($("#btn-language-submit").data("add-language-text")))})),$(document).on("click","#btn-language-submit",(function(a){a.preventDefault();var e=$("#lang_name").val(),n=$("#lang_locale").val(),l=$("#lang_code").val(),r=$("#flag_list").val(),o=$("#lang_order").val(),i=$("input[name=lang_rtl]:checked").val();t.createOrUpdateLanguage(0,e,n,l,r,o,i,0)})),$(document).on("click","#btn-language-submit-edit",(function(a){a.preventDefault();var e=$("#lang_id").val(),n=$("#lang_name").val(),l=$("#lang_locale").val(),r=$("#lang_code").val(),o=$("#flag_list").val(),i=$("#lang_order").val(),g=$("input[name=lang_rtl]:checked").val(),c=$(a.currentTarget);t.createOrUpdateLanguage(e,n,l,r,o,i,g,1,c)})),e.on("click",".deleteDialog",(function(t){t.preventDefault(),$(".delete-crud-entry").data("section",$(t.currentTarget).data("section")),$(".modal-confirm-delete").modal("show")})),$(".delete-crud-entry").on("click",(function(t){t.preventDefault(),$(".modal-confirm-delete").modal("hide");var n=$(t.currentTarget).data("section");Xmetr.showButtonLoading($(a)),$httpClient.make().delete(n).then((function(t){var a=t.data;a.data&&(e.find("i[data-id=".concat(a.data,"]")).unwrap(),$(".tooltip").remove()),e.find('button[data-section="'.concat(n,'"]')).closest("tr").remove(),Xmetr.showSuccess(a.message)})).finally((function(){Xmetr.hideButtonLoading($(a))}))})),e.on("click",".set-language-default",(function(t){t.preventDefault();var a=$(t.currentTarget);$httpClient.make().get(a.data("section")).then((function(t){var n=t.data,l=e.find("td > svg");l.closest("td svg").removeClass("text-yellow"),l.replaceWith('<a href="javascript:void(0);" data-section="'.concat(route("languages.set.default"),"?lang_id=").concat(l.data("id"),'" class="set-language-default text-decoration-none" data-bs-toggle="tooltip" data-bs-original-title="Choose ').concat(l.data("name"),' as default language">').concat(l.closest("td").html(),"</a>")),a.find("svg").unwrap().addClass("text-yellow"),$(".tooltip").remove(),Xmetr.showSuccess(n.message)}))})),e.on("click",".edit-language-button",(function(t){t.preventDefault();var a=$(t.currentTarget);$httpClient.make().get(a.data("url")).then((function(t){var a=t.data.data;$("#lang_id").val(a.lang_id),$("#lang_name").val(a.lang_name),$("#lang_locale").val(a.lang_locale).trigger("change"),$("#lang_code").val(a.lang_code).trigger("change"),$("#flag_list").val(a.lang_flag).trigger("change"),$('input[name=lang_rtl][value="'.concat(a.lang_is_rtl?1:0,'"]')).prop("checked",!0),$("#lang_order").val(a.lang_order),$("#btn-language-submit").prop("id","btn-language-submit-edit").text($("#btn-language-submit-edit").data("update-language-text"))}))})),$(document).on("submit","form.language-settings-form",(function(t){t.preventDefault();var a=$(t.currentTarget),e=a.find("button[type=submit]");Xmetr.showButtonLoading(e),$httpClient.make().postForm(a.prop("action"),new FormData(a[0])).then((function(t){var e=t.data;Xmetr.showSuccess(e.message),a.removeClass("dirty")})).finally((function(){Xmetr.hideButtonLoading(e)}))}))}}])&&a(e.prototype,n),l&&a(e,l),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,l}();$((function(){(new n).init()}))})();
//# sourceMappingURL=language.js.map