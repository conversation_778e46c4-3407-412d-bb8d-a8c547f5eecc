(()=>{var o,t={88:()=>{$((function(){$(document).on("click",".button-import-groups, .button-re-import",(function(o){o.preventDefault();var t=$(o.currentTarget);$httpClient.make().withButtonLoading(t).postForm(t.data("url")).then((function(o){var e=o.data;if(Xmetr.showSuccess(e.message),t.closest(".modal").length){t.closest(".modal").modal("hide");var i=$(".translations-table .table");i.length?i.DataTable().ajax.url(window.location.href).load():setTimeout((function(){window.location.reload()}),1e3)}else setTimeout((function(){window.location.reload()}),1e3)}))}))}))},3693:()=>{},7182:()=>{},3225:()=>{},863:()=>{},7262:()=>{},1291:()=>{},3546:()=>{},1547:()=>{},7583:()=>{},6667:()=>{},9119:()=>{},7603:()=>{},2151:()=>{},811:()=>{},8367:()=>{},9783:()=>{},2483:()=>{},9005:()=>{},1034:()=>{},2625:()=>{}},e={};function i(o){var r=e[o];if(void 0!==r)return r.exports;var n=e[o]={exports:{}};return t[o](n,n.exports,i),n.exports}i.m=t,o=[],i.O=(t,e,r,n)=>{if(!e){var a=1/0;for(O=0;O<o.length;O++){for(var[e,r,n]=o[O],d=!0,v=0;v<e.length;v++)(!1&n||a>=n)&&Object.keys(i.O).every((o=>i.O[o](e[v])))?e.splice(v--,1):(d=!1,n<a&&(a=n));if(d){o.splice(O--,1);var l=r();void 0!==l&&(t=l)}}return t}n=n||0;for(var O=o.length;O>0&&o[O-1][2]>n;O--)o[O]=o[O-1];o[O]=[e,r,n]},i.o=(o,t)=>Object.prototype.hasOwnProperty.call(o,t),(()=>{var o={6591:0,5306:0,3895:0,2296:0,6940:0,9168:0,7014:0,8066:0,508:0,4:0,5536:0,7800:0,9558:0,4400:0,2043:0,7924:0,487:0,8610:0,2062:0,7063:0,340:0};i.O.j=t=>0===o[t];var t=(t,e)=>{var r,n,[a,d,v]=e,l=0;if(a.some((t=>0!==o[t]))){for(r in d)i.o(d,r)&&(i.m[r]=d[r]);if(v)var O=v(i)}for(t&&t(e);l<a.length;l++)n=a[l],i.o(o,n)&&o[n]&&o[n][0](),o[n]=0;return i.O(O)},e=self.webpackChunk=self.webpackChunk||[];e.forEach(t.bind(null,0)),e.push=t.bind(null,e.push.bind(e))})(),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(88))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(2483))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(9005))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(1034))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(2625))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(3693))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(7182))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(3225))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(863))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(7262))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(1291))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(3546))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(1547))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(7583))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(6667))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(9119))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(7603))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(2151))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(811))),i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(8367)));var r=i.O(void 0,[5306,3895,2296,6940,9168,7014,8066,508,4,5536,7800,9558,4400,2043,7924,487,8610,2062,7063,340],(()=>i(9783)));r=i.O(r)})();
//# sourceMappingURL=translation.js.map