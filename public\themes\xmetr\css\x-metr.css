html,
body {
  scroll-behavior: smooth;
}

#burger-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #fff;
  z-index: 99;
  transition: .2s;
  opacity: 0;
  visibility: hidden;
}

#burger-menu p {
  margin: 0;
}

.mobile-menu {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 20;
}

.burger-menu--active {
  opacity: 1 !important;
  visibility: visible !important;
}

.burger-menu_icon {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  transition: .2s ease-in-out;
}

.burger-menu_icon--active {
  opacity: 1;
  visibility: visible;
  position: relative;
}

.burger-menu_loginBtn:hover {
  background: rgba(94, 45, 194, .1);
  border-color: transparent !important;
}

.burger-menu_loginBtn:hover p {
  color: #5E2DC2 !important;
}

.burger-menu_registerBtn:hover {
  background: transparent;
  border-color: #5E2DC2 !important;
}

.burger-menu_registerBtn:hover p {
  color: #000;
}

.x-badge {
  color: #717171;
  display: inline-block;
  padding: 5px 8px;
  background: #F7F7F7;
  border-radius: 5px;
  width: fit-content;
  font-size: 13px;
}

.x-badge--furniture {
  background-color: #EEF7FB;
}

.x-badge--children {
  background-color: #FBFAEE;
}

.x-badge--pets {
  background-color: #FBF6EE;
}

.x-favorite_icon {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  transition: .2s ease-in-out;
  top: 16px;
  left: 15px;
}

.x-favorite:not(.x-favorite--active) .x-favorite-notFilled {
  opacity: 1;
  visibility: visible;
}

.x-favorite--active .x-favorite-filled {
  opacity: 1;
  visibility: visible;
}

.apartment-swiper_pagination .swiper-pagination-bullet {
  width: 6px;
  height: 6px;
  background: #fff;
  opacity: 1;
}

.apartment-swiper_pagination .swiper-pagination-bullet-active {
  width: 10px;
  height: 10px;
}

.x-profile-select .dropdown-toggle {
  height: 100%;
  background: none;
  border: 1px solid #DDDDDD;
  border-radius: 10px;
}

.x-profile-select .filter-option {
  display: flex;
  align-items: center;
}

.x-profile-select .filter-option-inner-inner {
  color: #000;
}

.x-profile-select .disabled span {
  color: #999;
}

.x-profile-select .inner {
  max-height: 200px;
}

.modal::before {
  position: fixed !important;
}

@media only screen and (min-width: 720px) {
  .x-modal-scrollOffset::before {
    right: 17px !important;
  }
}

.x-image-scale {
  overflow: hidden;
}

.x-image-scale img {
  transition: .3s ease-in-out;
}

.x-image-scale:hover img {
  transform: scale(1.1) rotate(-1deg)
}

.x-button-toggle {
  padding: 11px 16px;
  background: #F7F7F7;
  border-radius: 10px;
  transition: .1s ease-in-out;
  flex-grow: 1;
}

.x-button-toggle p {
  transition: .1s ease-in-out;
}

.x-button-toggle:active {
  transform: scale(.95);
}

.x-button-toggle p {
  color: #000;
  font-size: 15px;
}

.x-button-toggle--active {
  background: #5E2DC2;
}

.x-button-toggle--active p {
  color: #fff;
}

.x-filter-map {
  opacity: 0;
  visibility: hidden;
  touch-action: none;
  pointer-events: none;
  transition: .2s ease-in-out;
}

.x-filter-map--active {
  opacity: 1;
  visibility: visible;
  touch-action: auto;
  pointer-events: all;
}

.x-property {
  border-radius: 10px;
}

.x-property-dragndrop--enter {
  background: #8c65db !important;
}

.x-property-dragndrop--enter p {
  color: #fff;
}

.x-select {
  cursor: pointer;
}

.x-select,
.x-select:focus,
.x-select:active {
  outline: none !important;
  appearance: none;

  -webkit-appearance: none;
  -moz-appearance: none;
  background: transparent;
  background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position-x: 100%;
  background-position-y: 50%;
}

.x-sub-checkbox_icon {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: .2s ease-in-out;
}

.x-sub-checkbox:not(.x-sub-checkbox--active) .x-sub-checkbox_icon--notActive {
  opacity: 1;
  visibility: visible;
}

.x-sub-checkbox--active .x-sub-checkbox_icon--active {
  opacity: 1;
  visibility: visible;
}

.x-propertyType-wrapper {
  display: none;
}

.x-propertyType-wrapper--active {
  display: flex;
}

.x-sub-premiumParticle {
  transition: .2s ease-in-out;
  opacity: 0;
}

.x-sub-plan--premium .x-sub-premiumParticle {
  opacity: 1;
}

.x-sub-plan--premium .x-sub-premiumParticle--alt {
  opacity: .75;
}

.x-sub-checkbox--active .x-sub-checkbox_body {
  display: flex;
}



.x-home-accordion_body {
  opacity: 0;
  transform: translateY(-16px);
  visibility: hidden;
  transition: .2s ease-in-out;
}

.x-home-accordion--active .x-home-accordion_arrow {
  transform: rotate(0);
}

.x-home-accordion--active .x-home-accordion_body {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.x-home-selectCity_option {
  padding: 3px 8px;
  border-radius: 5px;
  background: #F7F7F7;
  cursor: pointer;
}

.x-home-selectCity_option p {
  color: #717171;
  font-size: 13px;
}

.x-home-selectCity_option--active {
  background: #5E2DC2;
}

.x-home-selectCity_option--active p {
  color: #fff;
}

.x-home-rentList_item {
  cursor: pointer;
}

.x-home-rentList_item b {
  color: #000;
  font-weight: 700;
  font-size: 15px;
}

.x-home-rentList_item--active b {
  color: #5E2DC2;
}

.x-home-selectList_country {
  cursor: pointer;
}

.x-home-selectList_country b {
  color: #000;
  font-weight: 700;
  font-size: 15px;
}

.x-home-selectList_country--active b {
  color: #5E2DC2;
}

.x-language-speakOn_element {
  border: 2px solid #D6D6D7;
  border-radius: 32px;
  padding: 8px;
  background: transparent;
  transition: .1s ease-in-out;
}

.x-language-speakOn_element--active {
  border-color: transparent;
  background-color: #5E2DC2;
}



.x-radio-toggle p{
    padding: 11px 16px;
    background: #F7F7F7;
    border-radius: 10px;
    transition: 0.1s ease-in-out;
    margin: 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    font-size: 15px;
    color: #000;
}

.x-radio-toggle input {
    display: none;
}

.x-radio-toggle p:active {

    transform: scale(.95);
}

.x-radio-toggle input:checked + p {
    background: #5E2DC2;
    color: #fff;
    border-radius: 10px;
    padding: 11px 16px;
}


.x-checkbox-toggle p{
    padding: 11px 16px;
    background: #F7F7F7;
    border-radius: 10px;
    transition: 0.1s ease-in-out;
    margin: 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    font-size: 15px;
    color: #000;
}
.x-checkbox-toggle input {
    display: none;
}
.x-checkbox-toggle p:active {
    transform: scale(.95);
}
.x-checkbox-toggle input:checked + p {
    background: #5E2DC2;
    color: #fff;
    border-radius: 10px;
    padding: 11px 16px;
}
