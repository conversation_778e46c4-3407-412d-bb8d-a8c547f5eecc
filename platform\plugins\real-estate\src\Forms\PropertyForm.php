<?php

namespace Xmetr\RealEstate\Forms;

use Xmetr\Base\Facades\AdminHelper;
use Xmetr\Base\Facades\Assets;
use Xmetr\Base\Forms\FieldOptions\AutocompleteFieldOption;
use Xmetr\Base\Forms\FieldOptions\ContentFieldOption;
use Xmetr\Base\Forms\FieldOptions\DescriptionFieldOption;
use Xmetr\Base\Forms\FieldOptions\HtmlFieldOption;
use Xmetr\Base\Forms\FieldOptions\MediaImagesFieldOption;
use Xmetr\Base\Forms\FieldOptions\NameFieldOption;
use Xmetr\Base\Forms\FieldOptions\OnOffFieldOption;
use Xmetr\Base\Forms\FieldOptions\RepeaterFieldOption;
use Xmetr\Base\Forms\FieldOptions\StatusFieldOption;
use Xmetr\Base\Forms\FieldOptions\TextareaFieldOption;
use Xmetr\Base\Forms\Fields\AutocompleteField;
use Xmetr\Base\Forms\Fields\EditorField;
use Xmetr\Base\Forms\Fields\HtmlField;
use Xmetr\Base\Forms\Fields\MediaImagesField;
use Xmetr\Base\Forms\Fields\NumberField;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\Base\Forms\Fields\RepeaterField;
use Xmetr\Base\Forms\Fields\SelectField;
use Xmetr\Base\Forms\Fields\TextareaField;
use Xmetr\Base\Forms\Fields\TextField;
use Xmetr\Base\Forms\FormAbstract;
use Xmetr\Location\Fields\Options\SelectLocationFieldOption;
use Xmetr\Location\Fields\SelectLocationField;
use Xmetr\RealEstate\Enums\CustomFieldEnum;
use Xmetr\RealEstate\Enums\PropertyPeriodEnum;
use Xmetr\RealEstate\Enums\PropertyStatusEnum;
use Xmetr\RealEstate\Enums\PropertyTypeEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fields\CategoryMultiField;
use Xmetr\RealEstate\Forms\Fronts\Auth\FieldOptions\TextFieldOption;
use Xmetr\RealEstate\Http\Requests\PropertyRequest;
use Xmetr\RealEstate\Models\Currency;
use Xmetr\RealEstate\Models\CustomField;
use Xmetr\RealEstate\Models\Facility;
use Xmetr\RealEstate\Models\Feature;
use Xmetr\RealEstate\Models\Project;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Models\Suitable;
use stdClass;
use Xmetr\Base\Forms\FieldOptions\RadioFieldOption;
use Xmetr\Base\Forms\FieldOptions\SelectFieldOption;
use Xmetr\Base\Forms\Fields\MultiCheckListField;
use Xmetr\Base\Forms\Fields\RadioField;
use Xmetr\RealEstate\Enums\RentalPeriodEnum;
use Xmetr\RealEstate\Enums\RequiredDocumentEnum;

class PropertyForm extends FormAbstract
{
    public function setup(): void
    {
        Assets::usingVueJS()
            ->addStyles('datetimepicker')
            ->addScripts('input-mask')
            ->addStylesDirectly('vendor/core/plugins/real-estate/css/real-estate.css')
            ->addScriptsDirectly([
                'vendor/core/plugins/real-estate/js/real-estate.js',
                'vendor/core/plugins/real-estate/js/components.js',
            ]);

        $projects = Project::query()
            ->select('name', 'id')
            ->latest()
            ->get()
            ->mapWithKeys(fn (Project $item) => [$item->getKey() => $item->name]) // @phpstan-ignore-line
            ->all();

        $currencies = Currency::query()->pluck('title', 'id')->all();

        $selectedCategories = [];
        if ($this->getModel()) {
            /**
             * @var Property $property
             */
            $property = $this->getModel();

            $selectedCategories = $property->categories()->pluck('category_id')->all();
        }

        $selectedFeatures = [];
        if ($this->getModel()) {
            /**
             * @var Property $property
             */
            $property = $this->getModel();

            $selectedFeatures = $property->features()->pluck('id')->all();
        }

        $features = Feature::query()
            ->select('id', 'name')
            ->get()
            ->each(function ($item): void {
                $item->name = (string) $item->name;
            });

        $selectedSuitable = [];
        if ($this->getModel()) {
            /**
             * @var Property $property
             */
            $property = $this->getModel();

            $selectedSuitable = $property->suitable()->pluck('id')->all();
        }

        $suitable = Suitable::query()
            ->select('id', 'name')
            ->get()
            ->each(function ($item): void {
                $item->name = (string) $item->name;
            });

        $facilities = Facility::query()
            ->select('id', 'name')
            ->get()
            ->each(function ($item): void {
                $item->name = (string) $item->name;
            });

        if ($this->getModel()) {
            /**
             * @var Property $property
             */
            $property = $this->getModel();

            $selectedFacilities = $property->facilities()->select('re_facilities.id', 'distance')->get();
        } else {
            $selectedFacilities = collect();

            $oldSelectedFacilities = old('facilities', []);

            if (! empty($oldSelectedFacilities)) {
                foreach ($oldSelectedFacilities as $oldSelectedFacility) {
                    if (! isset($oldSelectedFacility['id']) || ! isset($oldSelectedFacility['distance'])) {
                        continue;
                    }

                    $item = new stdClass();
                    $item->id = $oldSelectedFacility['id'];
                    $item->distance = $oldSelectedFacility['distance'];

                    $selectedFacilities->add($item);
                }
            }
        }

        $squareUnit = setting('real_estate_square_unit', 'm²') ? sprintf('(%s)', setting('real_estate_square_unit', 'm²')) : null;

        if ($this->getModel() && is_in_admin(true)) {
            add_filter('base_action_form_actions_extra', function (?string $html) {
                return $html . view(
                    'plugins/real-estate::partials.forms.duplicate-button',
                    [
                            'url' => route('property.duplicate-property', $this->getModel()->id),
                            'label' => trans('plugins/real-estate::property.duplicate'),
                        ]
                )->render();
            });
        }

        $isInAdmin = AdminHelper::isInAdmin(true);

        $this
            ->model(Property::class)
            ->setValidatorClass(PropertyRequest::class)
            ->template('plugins/real-estate::partials.forms.property-form')
            ->add('name', TextField::class, NameFieldOption::make()->required())
            ->add('type', SelectField::class, [
                'label' => trans('plugins/real-estate::property.form.type'),
                'required' => true,
                'choices' => PropertyTypeEnum::labels(),
                'attr' => [
                    'id' => 'type',
                ],
            ])
            ->add('description', TextareaField::class, DescriptionFieldOption::make())
            ->add('original_description', TextareaField::class, TextareaFieldOption::make()
                ->label(trans('plugins/real-estate::property.form.original_description'))
                ->placeholder(trans('plugins/real-estate::property.form.original_description_placeholder'))
                ->rows(4))
            ->add(
                'is_featured',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('core/base::forms.is_featured'))
                    ->defaultValue(false)
            )
            ->add(
                'content',
                EditorField::class,
                ContentFieldOption::make()
                    ->label(trans('plugins/real-estate::property.form.content'))
                    ->allowedShortcodes()
            )
            ->add(
                'images[]',
                MediaImagesField::class,
                MediaImagesFieldOption::make()
                    ->label(trans('plugins/real-estate::property.form.images'))
                    ->selected($this->getModel()->id ? $this->getModel()->images : [])
            )
            ->when(is_plugin_active('location'), function (FormAbstract $form): void {
                $form->add(
                    'location_data',
                    SelectLocationField::class,
                    SelectLocationFieldOption::make()
                );
            })
            ->add(
                'location',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/real-estate::property.form.location'))
                    ->placeholder(trans('plugins/real-estate::property.form.location'))
                    ->maxLength(191)
            )
            ->add('rowOpen', HtmlField::class, [
                'html' => '<div class="row lat-long-row">',
            ])
            ->add('latitude', TextField::class, [
                'label' => trans('plugins/real-estate::property.form.latitude'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-6',
                ],
                'attr' => [
                    'placeholder' => 'Ex: 1.462260',
                    'data-counter' => 25,
                ],
                'help_block' => [
                    'tag' => 'a',
                    'text' => trans('plugins/real-estate::property.form.latitude_helper'),
                    'attr' => [
                        'href' => 'https://www.latlong.net/convert-address-to-lat-long.html',
                        'target' => '_blank',
                        'rel' => 'nofollow',
                    ],
                ],
            ])
            ->add('longitude', TextField::class, [
                'label' => trans('plugins/real-estate::property.form.longitude'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-6',
                ],
                'attr' => [
                    'placeholder' => 'Ex: 103.812530',
                    'data-counter' => 25,
                ],
                'help_block' => [
                    'tag' => 'a',
                    'text' => trans('plugins/real-estate::property.form.longitude_helper'),
                    'attr' => [
                        'href' => 'https://www.latlong.net/convert-address-to-lat-long.html',
                        'target' => '_blank',
                        'rel' => 'nofollow',
                    ],
                ],
            ])
            ->add('rowClose', 'html', [
                'html' => '</div>',
            ])
            ->add('rowOpen1', 'html', [
                'html' => '<div class="row">',
            ])
            ->add('number_bedroom', NumberField::class, [
                'label' => trans('plugins/real-estate::property.form.number_bedroom'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-3',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::property.form.number_bedroom'),
                ],
            ])
            ->add('number_bathroom', NumberField::class, [
                'label' => trans('plugins/real-estate::property.form.number_bathroom'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-3',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::property.form.number_bathroom'),
                ],
            ])
            ->add('number_floor', NumberField::class, [
                'label' => trans('plugins/real-estate::property.form.number_floor'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-3',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::property.form.number_floor'),
                ],
            ])
            ->add('square', NumberField::class, [
                'label' => trans('plugins/real-estate::property.form.square', ['unit' => $squareUnit]),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-3',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::property.form.square', ['unit' => $squareUnit]),
                ],
            ])
            ->add('rowClose1', 'html', [
                'html' => '</div>',
            ])
            ->add('rowOpen2', 'html', [
                'html' => '<div class="row">',
            ])
            ->add('price', TextField::class, [
                'label' => trans('plugins/real-estate::property.form.price'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-6',
                ],
                'attr' => [
                    'id' => 'price-number',
                    'placeholder' => trans('plugins/real-estate::property.form.price'),
                    'class' => 'form-control input-mask-number',
                    'data-thousands-separator' => RealEstateHelper::getThousandSeparatorForInputMask(),
                    'data-decimal-separator' => RealEstateHelper::getDecimalSeparatorForInputMask(),
                ],
            ])
            ->add('currency_id', 'customSelect', [
                'label' => trans('plugins/real-estate::property.form.currency'),
                'wrapper' => [
                    'class' => 'form-group mb-3 col-md-6',
                ],
                'attr' => [
                    'class' => 'select-full',
                ],
                'choices' => $currencies,
            ])
            ->add('period', 'customSelect', [
                'label' => trans('plugins/real-estate::property.form.period'),
                'required' => true,
                'wrapper' => [
                    'class' => 'form-group mb-3 period-form-group col-md-4' . ($this->getModel()->type != PropertyTypeEnum::RENT ? ' hidden' : null),
                ],
                'attr' => [
                    'class' => 'select-search-full',
                    'id' => 'period',
                ],
                'choices' => PropertyPeriodEnum::labels(),
            ])
            ->add('rowClose2', 'html', [
                'html' => '</div>',
            ])
            ->add('never_expired', 'onOff', [
                'label' => trans('plugins/real-estate::property.never_expired'),
                'default_value' => true,
            ])
            ->add('bills_included', OnOffField::class, [
                'label' => trans('plugins/real-estate::property.bills_included'),
                'default_value' => false,
                'attr' => [
                    'id' => 'bills_included',
                    'onchange' => 'toggleUtilitiesField(this)',
                ],
            ])
            ->add('utilities', TextField::class, [
                'label' => trans('plugins/real-estate::property.utilities'),
                'wrapper' => [
                    'class' => 'form-group mb-3 utilities-field',
                    'style' => $this->getModel()->bills_included ? 'display: none;' : '',
                ],
                'attr' => [
                    'placeholder' => trans('plugins/real-estate::property.utilities_placeholder'),
                    'class' => 'form-control input-mask-number',
                    'data-thousands-separator' => RealEstateHelper::getThousandSeparatorForInputMask(),
                    'data-decimal-separator' => RealEstateHelper::getDecimalSeparatorForInputMask(),
                ],
            ])
            ->add('highlights_label', HtmlField::class, [
                'html' => '<label class="form-section-title form-label">'.trans('plugins/real-estate::property.form.highlights').'</label>'
            ])
            ->add('highlights_group', HtmlField::class, [
                'html' => '<div class="row highlights-group">'
            ])
            ->add('furnished', OnOffField::class, [
                'label' => trans('plugins/real-estate::property.furnished'),
                'default_value' => false,
                'wrapper' => ['class' => 'form-group mb-3 col-md-3 highlight-item'],
            ])
            ->add('pets_allowed', OnOffField::class, [
                'label' => trans('plugins/real-estate::property.pets_allowed'),
                'default_value' => false,
                'wrapper' => ['class' => 'form-group mb-3 col-md-3 highlight-item'],
            ])
            ->add('smoking_allowed', OnOffField::class, [
                'label' => trans('plugins/real-estate::property.smoking_allowed'),
                'default_value' => false,
                'wrapper' => ['class' => 'form-group mb-3 col-md-3 highlight-item'],
            ])
            ->add('online_view_tour', OnOffField::class, [
                'label' => trans('plugins/real-estate::property.online_view_tour'),
                'default_value' => false,
                'wrapper' => ['class' => 'form-group mb-3 col-md-3 highlight-item'],
            ])
            ->add('highlights_group_end', HtmlField::class, [
                'html' => '</div>'
            ])
            ->add('rental_period', RadioField::class, [
                'label' => trans('plugins/real-estate::property.form.rental_period'),
                'wrapper' => [
                    'class' => 'form-group mb-3 rental-period-form-group',
                ],
                'attr' => [
                    'class' => 'select-search-full',
                ],
                'choices' => RentalPeriodEnum::labels(),
            ])
            ->add('required_documents[]', MultiCheckListField::class, [
                'label' => trans('plugins/real-estate::property.form.required_documents'),
                'label_attr' => ['class' => 'control-label'],
                'wrapper' => [
                    'class' => 'form-group mb-3 required_documents-form-group',
                ],
                'choices' => RequiredDocumentEnum::labels(),
            ])
            ->add(
                'private_notes',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/real-estate::property.private_notes'))
                    ->helperText(trans('plugins/real-estate::property.private_notes_helper'))
                    ->rows(2)
                    ->colspan(2)
            )
            ->add('auto_renew', 'onOff', [
                'label' => trans('plugins/real-estate::property.renew_notice', ['days' => RealEstateHelper::propertyExpiredDays()]),
                'default_value' => false,
                'wrapper' => [
                    'class' => 'form-group mb-3 auto-renew-form-group' . (! $this->getModel()->id || $this->getModel()->never_expired ? ' hidden' : null),
                ],
                'attr' => [
                    'id' => 'auto_renew',
                ],
            ])
            ->add(
                'floor_plans',
                RepeaterField::class,
                RepeaterFieldOption::make()
                    ->label(trans('plugins/real-estate::property.floor_plans.title'))
                    ->fields([
                        'name' => [
                            'type' => 'text',
                            'label' => trans('plugins/real-estate::property.floor_plans.name'),
                            'attributes' => [
                                'name' => 'name',
                                'value' => null,
                                'options' => [
                                    'class' => 'form-control',
                                    'data-counter' => 255,
                                ],
                            ],
                        ],
                        'description' => [
                            'type' => 'textarea',
                            'label' => trans('plugins/real-estate::property.floor_plans.description'),
                            'attributes' => [
                                'name' => 'description',
                                'value' => null,
                                'options' => [
                                    'class' => 'form-control',
                                    'rows' => 2,
                                ],
                            ],
                        ],
                        'image' => [
                            'type' => $isInAdmin ? 'mediaImage' : 'text',
                            'label' => trans('plugins/real-estate::property.floor_plans.image'),
                            'attributes' => [
                                'name' => 'image',
                                'value' => null,
                                'options' => $isInAdmin ? [] : [
                                    'class' => 'form-control',
                                    'placeholder' => trans('plugins/real-estate::property.floor_plans.image_placeholder'),
                                ],
                            ],
                        ],
                        'bedrooms' => [
                            'type' => 'number',
                            'label' => trans('plugins/real-estate::property.floor_plans.bedrooms'),
                            'attributes' => [
                                'name' => 'bedrooms',
                                'value' => null,
                                'options' => [
                                    'class' => 'form-control',
                                    'placeholder' => trans('plugins/real-estate::property.floor_plans.bedrooms_placeholder'),
                                ],
                            ],
                        ],
                        'bathrooms' => [
                            'type' => 'number',
                            'label' => trans('plugins/real-estate::property.floor_plans.bathrooms'),
                            'attributes' => [
                                'name' => 'bathrooms',
                                'value' => null,
                                'options' => [
                                    'class' => 'form-control',
                                    'placeholder' => trans('plugins/real-estate::property.floor_plans.bathrooms_placeholder'),
                                ],
                            ],
                        ],
                    ])
            )
            ->addMetaBoxes([
                'features' => [
                    'title' => trans('plugins/real-estate::property.form.features'),
                    'content' => view(
                        'plugins/real-estate::partials.form-features',
                        compact('selectedFeatures', 'features')
                    )->render(),
                    'priority' => 2,
                ],
                'suitable' => [
                    'title' => trans('plugins/real-estate::property.form.suitable'),
                    'content' => view(
                        'plugins/real-estate::partials.form-suitable',
                        compact('selectedSuitable', 'suitable')
                    )->render(),
                    'priority' => 1,
                ],
                'facilities' => [
                    'title' => trans('plugins/real-estate::property.distance_key'),
                    'content' => view(
                        'plugins/real-estate::partials.form-facilities',
                        compact('facilities', 'selectedFacilities')
                    ),
                    'priority' => 0,
                ],
            ])
            ->add('utilities_script', HtmlField::class, [
                'html' => '<script>
                    function toggleUtilitiesField(checkbox) {
                        const utilitiesField = document.querySelector(".utilities-field");
                        if (utilitiesField) {
                            if (checkbox.checked) {
                                utilitiesField.style.display = "none";
                            } else {
                                utilitiesField.style.display = "block";
                            }
                        }
                    }

                    document.addEventListener("DOMContentLoaded", function() {
                        const billsIncludedCheckbox = document.getElementById("bills_included");
                        if (billsIncludedCheckbox) {
                            toggleUtilitiesField(billsIncludedCheckbox);
                        }
                    });
                </script>',
            ])
            ->add(
                'status',
                RadioField::class,
                RadioFieldOption::make()
                    ->choices(PropertyStatusEnum::labels())
                    ->selected((string) $this->model->status ?: PropertyStatusEnum::NOT_AVAILABLE)
                    ->attributes(['class' => 'custom-radio'])
                    ->wrapperAttributes(['class' => 'mb-3'])
                    ->metadata()
            )


            ->when($this->getModel()->exists, function (FormAbstract $form): void {
                $form->add(
                    'moderation_status',
                    HtmlField::class,
                    HtmlFieldOption::make()
                        ->label(trans('plugins/real-estate::property.moderation_status'))
                        ->content(view('plugins/real-estate::partials.moderation-status', [
                            'model' => $this->getModel(),
                        ])->render())
                );
            })
            ->add('categories[]', CategoryMultiField::class, [
                'label' => trans('plugins/real-estate::property.form.categories'),
                'choices' => get_property_categories_with_children(),
                'value' => old('categories', $selectedCategories),
            ])
            ->add(
                'unique_id',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/real-estate::property.unique_id'))
                    ->placeholder(trans('plugins/real-estate::property.unique_id'))
                    ->value($this->getModel()->getKey() ? $this->getModel()->unique_id : $this->getModel()->generateUniqueId())
                    ->maxLength(120)
            )
            ->when(! empty($projects), function () use ($projects): void {
                $this
                    ->add('project_id', 'customSelect', [
                        'label' => trans('plugins/real-estate::property.form.project'),
                        'attr' => [
                            'class' => 'select-search-full',
                        ],
                        'choices' => [0 => trans('plugins/real-estate::property.select_project')] + $projects,
                    ]);
            })
            ->setBreakFieldPoint('status')
            ->add(
                'author_id',
                AutocompleteField::class,
                AutocompleteFieldOption::make()
                    ->label(trans('plugins/real-estate::property.account'))
                    ->ajaxUrl(route('account.list'))
                    ->when($this->getModel()->author_id, function (AutocompleteFieldOption $option): void {
                        $option->choices([$this->model->author->id => $this->model->author->name .' - '. ($this->model->author->city->name ?? 'no-city') . ' - ' . $this->model->author->id . ' - ' . $this->model->author->email]);
                    })
                    ->emptyValue(trans('plugins/real-estate::property.select_account'))
                    ->allowClear()
            )
            ->when(RealEstateHelper::isEnabledCustomFields(), function (FormAbstract $form): void {
                Assets::addScriptsDirectly('vendor/core/plugins/real-estate/js/custom-fields.js');

                $customFields = CustomField::query()->select(['name', 'id', 'type'])->get();

                $form->addMetaBoxes([
                    'custom_fields_box' => [
                        'title' => trans('plugins/real-estate::custom-fields.name'),
                        'content' => view('plugins/real-estate::custom-fields.custom-fields', [
                            'options' => CustomFieldEnum::labels(),
                            'customFields' => $customFields,
                            'model' => $this->model,
                            'ajax' => is_in_admin(true) ? route('real-estate.custom-fields.get-info') : route('public.account.custom-fields.get-info'),
                        ]),
                        'priority' => 0,
                    ],
                ]);
            });
    }
}


