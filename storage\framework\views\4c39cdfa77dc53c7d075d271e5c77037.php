<ul<?php echo BaseHelper::clean($options); ?>>
    <?php $__currentLoopData = $menu_nodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="<?php echo \Illuminate\Support\Arr::toCssClasses(['visible_list','dropdown2' => $row->has_child, 'current' => $row->active, $row->css_class]); ?>">
            <a href="<?php echo e($row->url); ?>" target="<?php echo e($row->target); ?>" class="list-item !max-[1020px]:px-[12px]">
                <?php echo BaseHelper::clean($row->icon_html); ?>

                <span class="title text-black"><?php echo e($row->title); ?></span>
            </a>

            <?php if($row->has_child): ?>
                <?php echo Menu::generateMenu([
                    'menu' => $menu,
                    'menu_nodes' => $row->child,
                    'view' => 'main-menu',
                ]); ?>

            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/partials/main-menu.blade.php ENDPATH**/ ?>