# XMETR Language Plugin Changes

This document outlines the changes made to the Language plugin for XMETR integration.

## Files Modified

### 1. AddHrefLangListener.php
**File:** `platform/plugins/language/src/Listeners/AddHrefLangListener.php`

**Changes:**
- Updated the else block logic for handling slug-based URL generation
- Improved URL construction for multilingual content with proper prefix handling

**Code Updated (else block):**
```php
if ($referenceType == Page::class && ! $referenceId) {
    foreach (Language::getSupportedLocales() as $localeCode => $properties) {
        $urls[] = [
            'url' => Language::getLocalizedURL($localeCode, url()->current(), [], false),
            'lang_code' => $localeCode,
        ];
    }
} else {
    $slug = Slug::query()->where('reference_id',  $event->slug->reference_id)
        ->where('reference_type', $event->slug->reference_type)
        ->with('translations')
        ->first();

    $urls[] = [
        'url' => url($defaultLocale . ($slug->prefix ? '/' . $slug->prefix : '') . '/' . $slug->key),
        'lang_code' => $defaultLocale,
    ];
    
    foreach ($slug->translations as $item) {
        if (empty($item->lang_code)) {
            continue;
        }

        $locale = Language::getLocaleByLocaleCode($item->lang_code);

        if ($locale == $defaultLocale && Language::hideDefaultLocaleInURL()) {
            $locale = null;
        }

        $urls[] = [
            'url' => url($locale . ($item->prefix ? '/' . $item->prefix : '') . '/' . $item->key),
            'lang_code' => $locale,
        ];
    }
}
```

### 2. hreflang.blade.php
**File:** `platform/plugins/language/resources/views/partials/hreflang.blade.php`

**Changes:**
- Complete rewrite of the template to handle both custom URLs and fallback scenarios
- Added conditional logic to use custom URLs when available or fallback to default behavior

**Complete Updated Code:**
```blade
@if (!empty($urls))

@foreach ($urls as $item)
<link rel="alternate" hreflang="{{ $item['lang_code'] }}" href="{{ $item['url'] }}"/>
@endforeach

@else
@foreach (Language::getSupportedLocales() as $localeCode => $properties)
<link rel="alternate" hreflang="{{ $localeCode }}" href="{{ Language::getLocalizedURL($localeCode, url()->current(), [], false) }}"/>
@endforeach
@endif
```

## Summary of Changes

### 1. Enhanced URL Generation Logic
- **Improved slug handling**: The AddHrefLangListener now properly handles slug translations with prefix support
- **Better locale management**: Enhanced logic for handling default locale visibility in URLs
- **Robust URL construction**: Improved URL building that accounts for prefixes and locale-specific paths

### 2. Flexible Hreflang Template
- **Conditional rendering**: The template now checks if custom URLs are provided before falling back to default behavior
- **Fallback mechanism**: When no custom URLs are available, it uses the standard Language::getLocalizedURL method
- **Clean output**: Generates proper hreflang meta tags for SEO optimization

## Benefits

1. **Better SEO**: Proper hreflang tags for multilingual content improve search engine understanding
2. **Flexible URL handling**: Supports both custom slug-based URLs and standard localized URLs
3. **Prefix support**: Handles URL prefixes correctly for different content types
4. **Locale management**: Properly manages default locale visibility based on configuration
5. **Fallback safety**: Ensures hreflang tags are always generated, even when custom URLs aren't available

## Technical Details

### URL Structure
- Default locale URLs: `/{prefix}/{slug-key}` (when default locale is hidden)
- Localized URLs: `/{locale}/{prefix}/{slug-key}`
- Prefix is optional and depends on the content type configuration

### Slug Translation Handling
- Queries slug translations using reference_id and reference_type
- Iterates through all available translations
- Constructs proper URLs for each language variant
- Handles empty language codes gracefully

## Notes

- The changes maintain backward compatibility with existing functionality
- Custom URLs take precedence over default URL generation
- The system gracefully handles missing translations or empty language codes
- URL construction respects the "hide default locale" configuration setting
