<?php

return [
    'name' => 'Properties',
    'properties' => 'Properties',
    'create' => 'New property',
    'edit' => 'Edit property',
    'form' => [
        'main_info' => 'General information',
        'basic_info' => 'Basic information',
        'name' => 'Title',
        'type' => 'Type',
        'images' => 'Images',
        'location' => 'Property location',
        'number_bedroom' => 'Number bedrooms',
        'number_bathroom' => 'Number bathrooms',
        'number_floor' => 'Number floors',
        'square' => 'Square :unit',
        'price' => 'Price',
        'deposit' => 'Deposit',
        'features' => 'Features',
        'suitable' => 'Suitable For',
        'project' => 'Project',
        'date' => 'Date information',
        'currency' => 'Currency',
        'city' => 'City',
        'period' => 'Period',
        'category' => 'Category',
        'latitude' => 'Latitude',
        'latitude_helper' => 'Go here to get Latitude from address.',
        'longitude' => 'Longitude',
        'longitude_helper' => 'Go here to get Longitude from address.',
        'categories' => 'Categories',
        'images_upload_placeholder' => 'Drop files here or click to upload.',
        'content' => 'Content',
        'description' => 'Description',
        'description_placeholder' => 'Enter the description of the property',
        'original_description' => 'Original Description',
        'original_description_placeholder' => 'Enter the original description of the property',
        'highlights' => 'Highlights',
        'rental_period' => 'Rental Period',
        'rental_periods' => [
            'min_1_month' => 'Min. 1 month',
            'min_3_months' => 'Min. 3 months',
            'min_6_months' => 'Min. 6 months',
            'min_1_year' => 'Min. 1 year',
        ],
        'required_documents' => 'Required Documents',
        'documents' => [
            'passport_id' => 'Passport / ID',
            'income_statement' => 'Income Statement',
            'bank_statement' => 'Bank Statement',
            'employment_contract' => 'Employment Contract',
            'proof_employment_studies' => 'Proof of Employment / Studies',
            'residence_permit_visa' => 'Residence Permit / Visa',
        ],
    ],
    'statuses' => [
        'not_available' => 'Not available',
        'pre_sale' => 'Preparing selling',
        'selling' => 'Selling',
        'sold' => 'Sold',
        'renting' => 'Renting',
        'rented' => 'Rented',
        'building' => 'Building',
    ],
    'types' => [
        'sale' => 'Sale',
        'rent' => 'Rent',
    ],
    'periods' => [
        'day' => 'Daily',
        'week' => 'Weekly',
        'month' => 'Monthly',
        'year' => 'Yearly',
    ],
    'moderation_status' => 'Moderation status',
    'moderation-statuses' => [
        'pending' => 'Pending',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
    ],
    'renew_notice' => 'Renew automatically (you will be charged again in :days days)?',
    'distance_key' => 'Distance key between facilities',
    'never_expired' => 'Never expired?',
    'bills_included' => 'Bills Included',
    'utilities' => 'Utilities',
    'utilities_placeholder' => 'Enter utilities cost',
    'furnished' => 'Furnished',
    'pets_allowed' => 'Pets Allowed',
    'smoking_allowed' => 'Smoking Allowed',
    'online_view_tour' => 'Online View Tour',
    'select_project' => 'Select a project...',
    'account' => 'Account',
    'select_account' => 'Select an account...',
    'expire_date' => 'Expire date',
    'never_expired_label' => 'Never expired',
    'active_properties' => 'Active properties',
    'pending_properties' => 'Pending properties',
    'expired_properties' => 'Expired properties',
    'import_properties' => 'Import Properties',
    'export_properties' => 'Export Properties',
    'duplicate_property_successfully' => 'Duplicated property successfully!',
    'duplicate' => 'Duplicate',
    'views' => 'Views',
    'unique_id' => 'Unique ID',
    'private_notes' => 'Private notes',
    'private_notes_helper' => 'Private notes are only visible to owner. It won\'t be shown on the frontend.',
    'floor_plans' => [
        'title' => 'Floor plans',
        'name' => 'Name',
        'description' => 'Description',
        'image' => 'Image',
        'bedrooms' => 'Bedrooms',
        'bedrooms_placeholder' => 'Enter number of bedrooms',
        'bathrooms' => 'Bathrooms',
        'bathrooms_placeholder' => 'Enter number of bathrooms',
        'image_placeholder' => 'Enter image URL. e.g: https://example.com/image.jpg',
    ],
    'status_moderation' => [
        'approve' => 'Approve',
        'reject' => 'Reject',
        'approve_title' => 'Approve property',
        'approve_message' => 'Are you sure you want to approve this property? This action cannot be undone.',
        'reject_title' => 'Reject property',
        'reject_message' => 'Are you sure you want to reject this property? This action cannot be undone.',
        'reject_reason' => 'Enter the reason for rejection',
        'approved' => 'Property has been approved successfully!',
        'rejected' => 'Property has been rejected successfully!',
        'reason_rejected' => 'Reason rejected',
    ]
];


