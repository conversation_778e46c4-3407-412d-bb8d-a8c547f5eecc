// Home page scripts
(function () {
  document.querySelectorAll('.x-home-accordion_trigger').forEach(el => el.addEventListener('click', toggleAccordion))

  function toggleAccordion(e) {
    const wrapper = e.target.closest('.x-home-accordion')

    document.querySelectorAll('.x-home-accordion').forEach(el => {
      if (el !== wrapper) el.classList.remove('x-home-accordion--active')
    })

    wrapper.classList.toggle('x-home-accordion--active')
  }

  window.addEventListener('click', e => {
    // Skip if clicked on trigger
    if (e.target.closest('.x-home-accordion_trigger')) return;

    if (!e.target.closest('.x-home-accordion')) {
      document.querySelectorAll('.x-home-accordion').forEach(el => el.classList.remove('x-home-accordion--active'))
    }

    document.querySelectorAll('.x-home-accordion--closeOnAny').forEach(el => {
      if (el.classList.contains('x-home-accordion--active')) el.classList.remove('x-home-accordion--active')
    })
  })

  document.querySelectorAll('.x-home-selectList_country').forEach(el => el.addEventListener('click', toggleCountry))

  function toggleCountry(e) {
    const wrapper = e.target.closest('.x-home-selectList_country');
    document.querySelectorAll('.x-home-selectList_country').forEach(el => el.classList.remove('x-home-selectList_country--active'))

    wrapper.classList.add('x-home-selectList_country--active');
    wrapper.closest('.x-home-accordion').querySelector('.x-home-accordion_label').innerText = wrapper.querySelector('b').innerText

    const dataValue = wrapper.dataset.value; // Adjust 'value' to your data attribute name
    // Set the the action of #hero-search-form to dataValue
    const form = document.querySelector('#hero-search-form');
    if (form) {
        form.action = dataValue;
    }
    wrapper.closest('.x-home-accordion').classList.toggle('x-home-accordion--active')

  }

   document.querySelector('#home-country-select').addEventListener('change', function(e) {
    const selectValue = e.target.value;
    const form = document.querySelector('#hero-search-form');
    if (form) {
        form.action = selectValue;
    }
   });

   document.querySelectorAll('.x-home-rentList_item').forEach(el => el.addEventListener('click', toggleRent))

  function toggleRent(e) {
    const wrapper = e.target.closest('.x-home-rentList_item');
    document.querySelectorAll('.x-home-rentList_item').forEach(el => el.classList.remove('x-home-rentList_item--active'))

    wrapper.classList.add('x-home-rentList_item--active');
    wrapper.closest('.x-home-accordion').querySelector('.x-home-accordion_label').innerText = wrapper.querySelector('b').innerText

    const dataValue = wrapper.dataset.value; // Adjust 'value' to your data attribute name

         // Set the value of the hidden input field
        const categoryInput = document.querySelector('#category_id');
        if (categoryInput) {
            categoryInput.value = dataValue;
        }

  }

//   document.querySelector('#x-home-selectCity').addEventListener('change', async (e) => {
//     const value = e.target.value;

//     const country = document.querySelector('#x-home-countryLabel');
//     const sign = document.querySelector('#x-home-countrySign');
//     const city = document.querySelector('#x-home-countryCityLabel');
//     const label = document.querySelector(`[value="${value}"]`).innerText;

//     if (value !== 'city-argentina') {
//       city.innerText = label;
//       sign.style.display = 'none';
//     } else {
//       city.innerText = 'Аргентина, Буэнос-Айрес'
//       sign.style.display = 'block';
//     }

//     country.innerText = label;

//     const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
//     const wrapper = document.querySelector('#x-home-selectCity_wrapper');
//     const url = wrapper.dataset.url;
//     // Request data
//     const data = {
//         country_id: value,
//     };

//     // Show loading spinner
//     wrapper.innerHTML = '<div class="loading-spinner"></div>';

//     try {
//         const response = await fetch(url, {
//           method: 'POST', // Use POST method
//           headers: {
//             'Content-Type': 'application/json',
//             'X-CSRF-TOKEN': csrfToken, // Add CSRF token header
//           },
//           body: JSON.stringify(data), // Convert data to JSON string
//         });

//         if (!response.ok) {
//           throw new Error(`HTTP error! Status: ${response.status}`);
//         }

//         const result = await response.text();

//         wrapper.innerHTML = result; // Clear the spinner

//       } catch (error) {
//         console.error('Error fetching data:', error);
//         wrapper.innerHTML = '<p>Failed to load data. Please try again.</p>';
//       } finally {
//         const spinner = wrapper.querySelector('.loading-spinner');
//         if (spinner) {
//           spinner.remove();
//         }
//       }

//   })

//   document.querySelector('#x-home-selectCity_wrapper').addEventListener('click', (e) => {
//      // Check if the clicked element is an option
//     const selectedOption = e.target.closest('.x-home-selectCity_option');
//     if (selectedOption) {
//         const wrapper = e.currentTarget;

//         // Remove active class from all options
//         wrapper.querySelectorAll('.x-home-selectCity_option').forEach(el =>
//           el.classList.remove('x-home-selectCity_option--active')
//         );

//         // Add active class to the clicked option
//         selectedOption.classList.add('x-home-selectCity_option--active');

//         // Get the data attribute of the selected option
//         const dataValue = selectedOption.dataset.value; // Adjust 'value' to your data attribute name
//         console.log('Selected data attribute:', dataValue);

//          // Set the value of the hidden input field
//         const cityInput = document.querySelector('#city_id');
//         if (cityInput) {
//         cityInput.value = dataValue;
//         }

//       }
//   });


//   document.querySelectorAll('.x-home-selectCity_option').forEach(el => el.addEventListener('click', e => {
//     const wrapper = e.target.closest('#x-home-selectCity_wrapper')

//     wrapper.querySelectorAll('.x-home-selectCity_option').forEach(el => el.classList.remove('x-home-selectCity_option--active'))

//     e.target.closest('.x-home-selectCity_option').classList.add('x-home-selectCity_option--active')
//   }))

})()
