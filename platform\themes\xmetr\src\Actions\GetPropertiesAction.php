<?php

namespace Theme\Xmetr\Actions;

use Xmetr\RealEstate\Enums\ModerationStatusEnum;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Models\Property;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class GetPropertiesAction
{
    /**
     * @return \Illuminate\Database\Eloquent\Collection<\Xmetr\RealEstate\Models\Property|\Illuminate\Database\Eloquent\Model>
     */
    public function handle(?int $limit = 4, ?string $categoryId = null, ?string $type = null, bool $featured = false): Collection
    {
        return Property::query()
            ->where('moderation_status', ModerationStatusEnum::APPROVED)
            ->whereNotIn('status', RealEstateHelper::exceptedPropertyStatuses())
            ->when(
                $featured,
                fn (Builder $query, string $type) => $query->where('is_featured', $featured) // @phpstan-ignore-line
            )
            ->when(
                $type,
                fn (Builder $query, string $type) => $query->where('type', $type)
            )
            ->when(
                $categoryId,
                function (Builder $query, string $categoryId) {
                    // Check if categoryId is numeric (ID) or string (slug)
                    if (is_numeric($categoryId)) {
                        return $query->whereRelation('categories', 'id', $categoryId);
                    } else {
                        // Handle as slug
                        $categoryIds = get_property_categories_related_ids_by_slug($categoryId);
                        if ($categoryIds) {
                            return $query->whereHas('categories', function ($subQuery) use ($categoryIds) {
                                $subQuery->whereIn('re_categories.id', $categoryIds);
                            });
                        }
                        return $query;
                    }
                }
            )
            ->take($limit)
            ->latest()
            ->with([...RealEstateHelper::getPropertyRelationsQuery(), 'author'])
            ->get();
    }
}
