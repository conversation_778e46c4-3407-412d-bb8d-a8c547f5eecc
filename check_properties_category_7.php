<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Checking Properties with Category ID 7 ===\n\n";

// Check how many properties have category ID 7
$propertiesWithCategory7 = \Illuminate\Support\Facades\DB::table('re_property_categories')
    ->where('category_id', 7)
    ->count();

echo "Properties associated with category ID 7: {$propertiesWithCategory7}\n\n";

// Get some property IDs
$propertyIds = \Illuminate\Support\Facades\DB::table('re_property_categories')
    ->where('category_id', 7)
    ->limit(5)
    ->pluck('property_id');

echo "Sample property IDs with category 7: " . $propertyIds->implode(', ') . "\n\n";

// Check if these properties are published and approved
foreach ($propertyIds as $propertyId) {
    $property = \Xmetr\RealEstate\Models\Property::find($propertyId);
    if ($property) {
        echo "Property ID {$propertyId}: '{$property->name}' - Status: {$property->status}, Moderation: {$property->moderation_status}\n";
    }
}

echo "\n";

// Test the actual query that should be used
echo "=== Testing the actual query ===\n";

$query = \Xmetr\RealEstate\Models\Property::query()
    ->where('moderation_status', \Xmetr\RealEstate\Enums\ModerationStatusEnum::APPROVED)
    ->whereNotIn('status', \Xmetr\RealEstate\Supports\RealEstateHelper::exceptedPropertyStatuses())
    ->whereHas('categories', function ($query) {
        $query->whereIn('re_categories.id', [7]);
    });

$count = $query->count();
echo "Properties found with the actual query: {$count}\n";

if ($count > 0) {
    $properties = $query->limit(3)->get(['id', 'name', 'status', 'moderation_status']);
    foreach ($properties as $property) {
        echo "  - Property ID {$property->id}: '{$property->name}' - Status: {$property->status}, Moderation: {$property->moderation_status}\n";
    }
}

echo "\nDone!\n";
