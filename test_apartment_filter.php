<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Apartment Filter ===\n\n";

// Test our helper function
echo "1. Testing helper function:\n";
$categoryId = get_property_category_id_by_slug('apartment');
echo "   get_property_category_id_by_slug('apartment') = " . ($categoryId ?: 'NULL') . "\n";

$categoryIds = get_property_categories_related_ids_by_slug('apartment');
echo "   get_property_categories_related_ids_by_slug('apartment') = [" . implode(', ', $categoryIds) . "]\n\n";

// Test direct database query
echo "2. Testing direct database query:\n";
if ($categoryIds) {
    $query = \Xmetr\RealEstate\Models\Property::query()
        ->where('moderation_status', \Xmetr\RealEstate\Enums\ModerationStatusEnum::APPROVED)
        ->whereNotIn('status', \Xmetr\RealEstate\Supports\RealEstateHelper::exceptedPropertyStatuses())
        ->whereHas('categories', function ($query) use ($categoryIds) {
            $query->whereIn('re_categories.id', $categoryIds);
        });
    
    echo "   SQL: " . $query->toSql() . "\n";
    echo "   Bindings: " . json_encode($query->getBindings()) . "\n";
    
    $count = $query->count();
    echo "   Count: {$count}\n\n";
    
    if ($count > 0) {
        echo "3. Sample properties found:\n";
        $properties = $query->limit(3)->get(['id', 'name', 'status', 'moderation_status']);
        foreach ($properties as $property) {
            echo "   - Property ID {$property->id}: '{$property->name}'\n";
        }
    } else {
        echo "3. No properties found with this filter.\n";
    }
} else {
    echo "   No category IDs found, cannot test query.\n";
}

echo "\n";

// Test what properties exist with category 7
echo "4. Testing properties with category ID 7:\n";
$propertiesWithCategory7 = \Xmetr\RealEstate\Models\Property::query()
    ->where('moderation_status', \Xmetr\RealEstate\Enums\ModerationStatusEnum::APPROVED)
    ->whereNotIn('status', \Xmetr\RealEstate\Supports\RealEstateHelper::exceptedPropertyStatuses())
    ->whereHas('categories', function ($query) {
        $query->where('re_categories.id', 7);
    })
    ->count();

echo "   Properties with category 7: {$propertiesWithCategory7}\n";

// Test the PropertyRepository directly
echo "\n5. Testing PropertyRepository:\n";
$repository = app(\Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface::class);

$filters = ['type' => 'apartment'];
$params = [
    'paginate' => [
        'per_page' => 10,
        'current_paged' => 1,
    ],
];

try {
    $result = $repository->getProperties($filters, $params);
    echo "   Repository result count: " . $result->count() . "\n";
    echo "   Repository result total: " . $result->total() . "\n";
} catch (\Exception $e) {
    echo "   Repository error: " . $e->getMessage() . "\n";
}

echo "\nDone!\n";
