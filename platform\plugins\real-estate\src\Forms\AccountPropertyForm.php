<?php

namespace Xmetr\RealEstate\Forms;

use Xmetr\Base\Forms\FieldOptions\ContentFieldOption;
use Xmetr\Base\Forms\FieldOptions\OnOffFieldOption;
use Xmetr\Base\Forms\FieldOptions\TextareaFieldOption;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\Base\Forms\Fields\TextareaField;
use Xmetr\Base\Forms\FormFieldOptions;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Forms\Fields\CustomEditorField;
use Xmetr\RealEstate\Forms\Fields\MultipleUploadField;
use Xmetr\RealEstate\Http\Requests\AccountPropertyRequest;
use Xmetr\RealEstate\Models\Property;

class AccountPropertyForm extends PropertyForm
{
    public function setup(): void
    {
        parent::setup();



        $this
            ->model(Property::class)
            ->template('plugins/real-estate::account.forms.base')
            ->hasFiles()
            ->setValidatorClass(AccountPropertyRequest::class)
            ->remove('is_featured')
            ->remove('moderation_status')
            ->remove('content')
            ->remove('images[]')
            ->remove('never_expired')
            ->modify(
                'auto_renew',
                OnOffField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/real-estate::property.renew_notice', [
                        'days' => RealEstateHelper::propertyExpiredDays(),
                    ]))
                    ->defaultValue(false),
                true
            )
            ->remove('author_id')
            ->addAfter(
                'name',
                'original_description',
                TextareaField::class, TextareaFieldOption::make()
                ->label(trans('plugins/real-estate::property.form.description'))
                ->placeholder(trans('plugins/real-estate::property.form.description_placeholder'))
                ->rows(4)
            )
            ->addAfter(
                'content',
                'images',
                MultipleUploadField::class,
                FormFieldOptions::make()
                    ->label(trans('plugins/real-estate::account-property.images', [
                        'max' => RealEstateHelper::maxPropertyImagesUploadByAgent(),
                    ]))
            );
    }
}
