<?php

use Xmetr\Base\Enums\BaseStatusEnum;
use Xmetr\Base\Supports\SortItemsWithChildrenHelper;
use Xmetr\RealEstate\Repositories\Interfaces\CategoryInterface;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

if (! function_exists('get_property_categories')) {
    function get_property_categories(array $args = []): array
    {
        $indent = Arr::get($args, 'indent', '——');

        $repo = app(CategoryInterface::class);

        $categories = $repo->getCategories(Arr::get($args, 'select', ['*']), [
            'created_at' => 'DESC',
            'is_default' => 'DESC',
            'order' => 'DESC',
        ], Arr::get($args, 'conditions', []));

        $categories = sort_item_with_children($categories);

        foreach ($categories as $category) {
            $depth = (int) $category->depth;
            $indentText = str_repeat($indent, $depth);
            $category->indent_text = $indentText;
        }

        return $categories;
    }
}

if (! function_exists('get_property_categories_with_children')) {
    function get_property_categories_with_children(): array
    {
        $categories = app(CategoryInterface::class)
            ->allBy(['status' => BaseStatusEnum::PUBLISHED], [], ['id', 'name', 'parent_id']);

        return app(SortItemsWithChildrenHelper::class)
            ->setChildrenProperty('child_cats')
            ->setItems($categories)
            ->sort();
    }
}

if (! function_exists('get_property_categories_related_ids')) {
    function get_property_categories_related_ids(
        int|string|null $categoryId,
        array &$results = [],
        array|Collection|null $categories = null
    ): array {
        if ($categories instanceof Collection) {
            $list = $categories->where('parent_id', $categoryId);
            foreach ($list as $item) {
                $results[] = $item->id;

                $children = $categories->where('parent_id', $item->id);
                if ($children && $children->count()) {
                    $results = get_property_categories_related_ids($item->id, $results, $children);
                }
            }

            return $results;
        }

        $categories = app(CategoryInterface::class)->allBy([
            'status' => BaseStatusEnum::PUBLISHED,
        ], [], ['id', 'parent_id']);

        $category = $categories->firstWhere('id', $categoryId);

        if ($category) {
            $results[] = $categoryId;
            $results = get_property_categories_related_ids($categoryId, $results, $categories);
        }

        return array_filter($results);
    }
}

if (! function_exists('get_property_category_id_by_slug')) {
    function get_property_category_id_by_slug(string $slug): ?int
    {
        try {
            $prefix = \Xmetr\Slug\Facades\SlugHelper::getPrefix(\Xmetr\RealEstate\Models\Category::class);

            $slugRecord = \Xmetr\Slug\Facades\SlugHelper::getSlug(
                $slug,
                $prefix,
                \Xmetr\RealEstate\Models\Category::class
            );

            if (!$slugRecord) {
                // Try without prefix as fallback
                $slugRecord = \Xmetr\Slug\Facades\SlugHelper::getSlug(
                    $slug,
                    null,
                    \Xmetr\RealEstate\Models\Category::class
                );
            }

            if (!$slugRecord) {
                return null;
            }

            $category = \Xmetr\RealEstate\Models\Category::find($slugRecord->reference_id);

            if (!$category) {
                return null;
            }

            // Check if category is published (handle both enum and string values)
            $status = $category->status;
            $isPublished = $status === \Xmetr\Base\Enums\BaseStatusEnum::PUBLISHED
                || $status === 'published'
                || (is_object($status) && method_exists($status, 'getValue') && $status->getValue() === 'published')
                || (is_object($status) && property_exists($status, 'value') && $status->value === 'published');

            return $isPublished ? $category->id : null;
        } catch (\Exception $e) {
            // Log error and return null
            \Illuminate\Support\Facades\Log::error('Error in get_property_category_id_by_slug: ' . $e->getMessage());
            return null;
        }
    }
}

if (! function_exists('get_property_categories_related_ids_by_slug')) {
    function get_property_categories_related_ids_by_slug(string $slug): array
    {
        $categoryId = get_property_category_id_by_slug($slug);

        if (!$categoryId) {
            return [];
        }

        return get_property_categories_related_ids($categoryId);
    }
}
