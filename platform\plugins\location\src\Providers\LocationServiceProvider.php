<?php

namespace Xmetr\Location\Providers;

use Xmetr\Base\Facades\DashboardMenu;
use Xmetr\Base\Facades\MacroableModels;
use Xmetr\Base\Facades\PanelSectionManager;
use Xmetr\Base\Models\BaseModel;
use Xmetr\Base\PanelSections\PanelSectionItem;
use Xmetr\Base\Supports\DashboardMenuItem;
use Xmetr\Base\Supports\ServiceProvider;
use Xmetr\Base\Traits\LoadAndPublishDataTrait;
use Xmetr\DataSynchronize\PanelSections\ExportPanelSection;
use Xmetr\DataSynchronize\PanelSections\ImportPanelSection;
use Xmetr\LanguageAdvanced\Supports\LanguageAdvancedManager;
use Xmetr\Location\Facades\Location;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\Country;
use Xmetr\Location\Models\District;
use Xmetr\Location\Models\State;
use Xmetr\Location\Repositories\Eloquent\CityRepository;
use Xmetr\Location\Repositories\Eloquent\DistrictRepository;
use Xmetr\Location\Repositories\Eloquent\CountryRepository;
use Xmetr\Location\Repositories\Eloquent\StateRepository;
use Xmetr\Location\Repositories\Interfaces\CityInterface;
use Xmetr\Location\Repositories\Interfaces\CountryInterface;
use Xmetr\Location\Repositories\Interfaces\DistrictInterface;
use Xmetr\Location\Repositories\Interfaces\StateInterface;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Foundation\AliasLoader;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Schema;
use Xmetr\SeoHelper\Facades\SeoHelper;
use Xmetr\Slug\Facades\SlugHelper;

class LocationServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(CountryInterface::class, function () {
            return new CountryRepository(new Country());
        });

        $this->app->bind(StateInterface::class, function () {
            return new StateRepository(new State());
        });

        $this->app->bind(CityInterface::class, function () {
            return new CityRepository(new City());
        });

        $this->app->bind(DistrictInterface::class, function () {
            return new DistrictRepository(new District());
        });

        AliasLoader::getInstance()->alias('Location', Location::class);
    }

    public function boot(): void
    {

        SeoHelper::registerModule([
            Country::class,
            State::class,
            City::class,
            District::class,
        ]);

        SlugHelper::registerModule(Country::class, 'Country');
        SlugHelper::registerModule(State::class, 'State');
        SlugHelper::registerModule(City::class, 'City');
        SlugHelper::registerModule(District::class, 'District');

        SlugHelper::setPrefix(Country::class, 'country', true );
        SlugHelper::setPrefix(State::class, 'state', true );
        SlugHelper::setPrefix(City::class, 'city', true );
        SlugHelper::setPrefix(District::class, 'district', true );



        $this
            ->setNamespace('plugins/location')
            ->loadHelpers(['helpers', 'constants'])
            ->loadAndPublishConfigurations(['permissions', 'general'])
            ->loadAndPublishViews()
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->publishAssets();

        if (defined('LANGUAGE_MODULE_SCREEN_NAME') && defined('LANGUAGE_ADVANCED_MODULE_SCREEN_NAME')) {
            LanguageAdvancedManager::registerModule(Country::class, [
                'name',
                'nationality',
            ]);

            LanguageAdvancedManager::registerModule(State::class, [
                'name',
            ]);

            LanguageAdvancedManager::registerModule(City::class, [
                'name',
            ]);

            LanguageAdvancedManager::registerModule(District::class, [
                'name',
                'district_description',
                'district_ratings_html',
                'amenities_comment',
                'transport_comment',
                'safety_comment',
                'green_spaces_comment',
                'noise_comment',
                'rent_comment',
                'atmosphere_comment',
            ]);
        }

        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-location')
                        ->priority(900)
                        ->name('plugins/location::location.name')
                        ->icon('ti ti-world')
                        ->permissions(['country.index'])
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-country')
                        ->priority(0)
                        ->parentId('cms-plugins-location')
                        ->name('plugins/location::country.name')
                        ->icon('ti ti-flag')
                        ->route('country.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-state')
                        ->priority(10)
                        ->parentId('cms-plugins-location')
                        ->name('plugins/location::state.name')
                        ->icon('ti ti-map')
                        ->route('state.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-city')
                        ->priority(20)
                        ->parentId('cms-plugins-location')
                        ->name('plugins/location::city.name')
                        ->icon('ti ti-location-pin')
                        ->route('city.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-district')
                        ->priority(25)
                        ->parentId('cms-plugins-location')
                        ->name('plugins/location::district.name')
                        ->icon('ti ti-map-pin')
                        ->route('district.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-location-bulk-import')
                        ->priority(30)
                        ->parentId('cms-plugins-location')
                        ->name('plugins/location::bulk-import.name')
                        ->icon('ti ti-package-import')
                        ->route('location.bulk-import.index')
                )
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-location-export')
                        ->priority(40)
                        ->parentId('cms-plugins-location')
                        ->name('plugins/location::export.name')
                        ->icon('ti ti-package-export')
                        ->route('location.export.index')
                );
        });

        PanelSectionManager::setGroupId('data-synchronize')->beforeRendering(function (): void {
            PanelSectionManager::default()
                ->registerItem(
                    ExportPanelSection::class,
                    fn () => PanelSectionItem::make('location')
                        ->setTitle(trans('plugins/location::location.name'))
                        ->withDescription(trans('plugins/location::location.export.description'))
                        ->withPriority(100)
                        ->withRoute('location.export.index')
                )
                ->registerItem(
                    ImportPanelSection::class,
                    fn () => PanelSectionItem::make('location')
                        ->setTitle(trans('plugins/location::location.name'))
                        ->withDescription(trans('plugins/location::location.import.description'))
                        ->withPriority(90)
                        ->withRoute('location.bulk-import.index')
                );
        });

        $this->app->booted(function (): void {

            Blueprint::macro('location', function ($item = null, $keys = []) {
                if ($item) {
                    if (class_exists($item) && Location::isSupported($item)) {
                        $data = Location::getSupported($item);
                        $model = new $item();
                        $table = $model->getTable();
                        $connection = $model->getConnectionName();
                        $keys = [];
                        foreach ($data as $key => $column) {
                            if (! Schema::connection($connection)->hasColumn($table, $column)) {
                                $keys[$key] = $column;
                            }
                        }
                    }
                } else {
                    $keys = array_filter(
                        array_merge([
                            'country' => 'country_id',
                            'state' => 'state_id',
                            'city' => 'city_id',
                            'district' => 'district_id',
                        ], $keys)
                    );
                }

                /**
                 * @var Blueprint $this
                 */
                if ($columnName = Arr::get($keys, 'country')) {
                    $this->foreignId($columnName)->default(1)->nullable();
                }

                if ($columnName = Arr::get($keys, 'state')) {
                    $this->foreignId($columnName)->nullable();
                }

                if ($columnName = Arr::get($keys, 'city')) {
                    $this->foreignId($columnName)->nullable();
                }

                if ($columnName = Arr::get($keys, 'district')) {
                    $this->foreignId($columnName)->nullable();
                }

                return true;
            });

            foreach (Location::getSupported() as $item => $keys) {
                if (! class_exists($item)) {
                    continue;
                }

                if ($foreignKey = Arr::get($keys, 'country')) {
                    /**
                     * @var BaseModel $item
                     */
                    $item::resolveRelationUsing('country', function ($model) use ($foreignKey) {
                        return $model->belongsTo(Country::class, $foreignKey)->withDefault();
                    });

                    MacroableModels::addMacro($item, 'getCountryNameAttribute', function () {
                        /**
                         * @var BaseModel $this
                         */
                        return $this->country->name;
                    });
                }

                if ($foreignKey = Arr::get($keys, 'state')) {
                    /**
                     * @var BaseModel $item
                     */
                    $item::resolveRelationUsing('state', function ($model) use ($foreignKey) {
                        return $model->belongsTo(State::class, $foreignKey)->withDefault();
                    });

                    MacroableModels::addMacro($item, 'getStateNameAttribute', function () {
                        /**
                         * @var BaseModel $this
                         */
                        return $this->state->name;
                    });
                }

                if ($foreignKey = Arr::get($keys, 'city')) {
                    /**
                     * @var BaseModel $item
                     */
                    $item::resolveRelationUsing('city', function ($model) use ($foreignKey) {
                        return $model->belongsTo(City::class, $foreignKey)->withDefault();
                    });

                    MacroableModels::addMacro($item, 'getCityNameAttribute', function () {
                        /**
                         * @var BaseModel $this
                         */
                        return $this->city->name;
                    });
                }

                if ($foreignKey = Arr::get($keys, 'district')) {
                    /**
                     * @var BaseModel $item
                     */
                    $item::resolveRelationUsing('district', function ($model) use ($foreignKey) {
                        return $model->belongsTo(District::class, $foreignKey)->withDefault();
                    });

                    MacroableModels::addMacro($item, 'getDistrictNameAttribute', function () {
                        /**
                         * @var BaseModel $this
                         */
                        return $this->district->name;
                    });
                }

                MacroableModels::addMacro($item, 'getFullAddressAttribute', function () {
                    /**
                     * @var BaseModel $this
                     */
                    $addresses = [$this->address, $this->district_name, $this->city_name, $this->state_name, $this->country_name];

                    return implode(', ', array_filter($addresses));
                });
            }
        });

        $this->app->register(CommandServiceProvider::class);
        $this->app->register(HookServiceProvider::class);
        $this->app->register(EventServiceProvider::class);
    }
}
