<?php

namespace Xmetr\RealEstate\Services;

use Xmetr\Base\Supports\Helper;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\RealEstate\Facades\RealEstateHelper;
use Xmetr\RealEstate\Models\Account;
use Xmetr\RealEstate\Models\Category;
use Xmetr\RealEstate\Models\Project;
use Xmetr\RealEstate\Models\Property;
use Xmetr\RealEstate\Repositories\Interfaces\ProjectInterface;
use Xmetr\RealEstate\Repositories\Interfaces\PropertyInterface;
use Xmetr\SeoHelper\Facades\SeoHelper;
use Xmetr\SeoHelper\SeoOpenGraph;
use Xmetr\Slug\Models\Slug;
use Xmetr\Theme\Facades\Theme;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Xmetr\Base\Facades\Html;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\Country;
use Xmetr\Page\Models\Page;

class HandleFrontPages
{
    public function handle(Slug|array $slug)
    {
        if (! $slug instanceof Slug) {
            return $slug;
        }

        $request = request();

        $isPreviewing = $request->input('preview') && (Auth::guard()->check() || Auth::guard('account')->check());

        switch ($slug->reference_type) {
            case Property::class:

                $condition = [
                    'id' => $slug->reference_id,
                ];

                if (! $isPreviewing) {
                    $condition = [
                        ...$condition,
                        ...RealEstateHelper::getPropertyDisplayQueryConditions(),
                    ];
                }

                $property = Property::query()->where($condition)->firstOrFail();

                if ($property->slugable->key !== $slug->key) {
                    return redirect()->to($property->url);
                }

                SeoHelper::setTitle($property->name)
                    ->setDescription(Str::words($property->description, 120));

                $meta = new SeoOpenGraph();
                if ($property->image) {
                    $meta->setImage(RvMedia::getImageUrl($property->image));
                }
                $meta->setDescription($property->description);
                $meta->setUrl($property->url);
                $meta->setTitle($property->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                SeoHelper::meta()->setUrl($property->url);

                if (theme_option('properties_list_page_id')) {
                    $propertiesListPage =  Page::query()
                        ->wherePublished()
                        ->where('id', theme_option('properties_list_page_id'))
                        ->select(['id', 'name'])
                        ->with(['slugable'])
                        ->first();
                    Theme::breadcrumb()->add(__('Properties'), $propertiesListPage->url);
                }
                Theme::breadcrumb()
                    ->add($property->city->country->name, $property->city->country->url)
                    ->add($property->city->name, $property->city->url);
                Theme::breadcrumb()->add('id' . $property->id);

                Helper::handleViewCount($property, 'viewed_property');
                RealEstateHelper::handleViewCountDailyBases($property, 'viewed_property_daily');

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, PROPERTY_MODULE_SCREEN_NAME, $property);

                add_filter(THEME_FRONT_HEADER, function (?string $html) use ($property): string|null {
                    $property->loadMissing('metadata');
                    $seo_meta = $property->getMetaData('seo_meta', true);

                    $schema = [
                        '@context' => 'https://schema.org',
                        '@type' => 'Product',
                        'name' => $property->name,
                        'image' => [
                            RvMedia::getImageUrl($property->image),
                        ],
                        'description' => $seo_meta['seo_description'] ?? Str::limit(strip_tags($property->content), 400),
                        'url' => $property->url,
                        'offers' => [
                            '@type' => 'Offer',
                            'url' => $property->url,
                            'priceCurrency' => get_application_currency()->title,
                            'price' => format_price($property->price, get_application_currency(), true),
                            'availability' => 'https://schema.org/InStock',
                        ],

                    ];


                    return $html . Html::tag('script', json_encode($schema, JSON_UNESCAPED_UNICODE), ['type' => 'application/ld+json'])
                        ->toHtml();
                }, 2);

                if (function_exists('admin_bar')) {
                    admin_bar()->registerLink(__('Edit this property'), route('property.edit', $property->id));
                }

                $images = [];
                if (! empty($property->images) && is_array($property->images)) {
                    foreach ($property->images as $image) {
                        $images[] = RvMedia::getImageUrl($image, null, false, RvMedia::getDefaultImage());
                    }
                }

                return [
                    'view' => 'real-estate.property',
                    'default_view' => 'plugins/real-estate::themes.property',
                    'data' => compact('property', 'images'),
                    'slug' => $property->slug,
                ];

            case Project::class:
                abort_unless(RealEstateHelper::isEnabledProjects(), 404);

                $condition = [
                    'id' => $slug->reference_id,
                ];

                if (! $isPreviewing) {
                    $condition = [
                        ...$condition,
                        ...RealEstateHelper::getProjectDisplayQueryConditions(),
                    ];
                }

                /**
                 * @var Project $project
                 */
                $project = Project::query()->where($condition)->firstOrFail();

                if ($project->slugable->key !== $slug->key) {
                    return redirect()->to($project->url);
                }

                SeoHelper::setTitle($project->name)
                    ->setDescription(Str::words($project->description, 120));

                $meta = new SeoOpenGraph();
                if ($project->image) {
                    $meta->setImage(RvMedia::getImageUrl($project->image));
                }
                $meta->setDescription($project->description);
                $meta->setUrl($project->url);
                $meta->setTitle($project->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                SeoHelper::meta()->setUrl($project->url);

                Theme::breadcrumb()
                    ->add(__('Projects'), route('public.projects'))
                    ->add($project->name);

                $relatedProjects = app(ProjectInterface::class)->getRelatedProjects(
                    $project->getKey(),
                    (int) theme_option('number_of_related_projects', 8)
                );

                if (function_exists('admin_bar')) {
                    admin_bar()->registerLink(__('Edit this project'), route('project.edit', $project->id));
                }

                Helper::handleViewCount($project, 'viewed_project');

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, PROJECT_MODULE_SCREEN_NAME, $project);

                $images = [];

                if (! empty($project->images) && is_array($project->images)) {
                    foreach ($project->images as $image) {
                        $images[] = RvMedia::getImageUrl($image, null, false, RvMedia::getDefaultImage());
                    }
                }

                return [
                    'view' => 'real-estate.project',
                    'default_view' => 'plugins/real-estate::themes.real-estate',
                    'data' => compact('project', 'images', 'relatedProjects'),
                    'slug' => $project->slug,
                ];

            case Category::class:
                $category = Category::query()
                    ->where('id', $slug->reference_id)
                    ->with(['slugable'])
                    ->firstOrFail();

                SeoHelper::setTitle($category->name)
                    ->setDescription(Str::words($category->description, 120));

                $meta = new SeoOpenGraph();
                $meta->setDescription($category->description);
                $meta->setUrl($category->url);
                $meta->setTitle($category->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                Theme::breadcrumb()->add($category->name);

                $filters = [
                    'category_id' => $category->getKey(),
                ];

                $perPage = (int) theme_option('number_of_properties_per_page', 12);

                $params = [
                    'paginate' => [
                        'per_page' => $perPage ?: 12,
                        'current_paged' => $request->integer('page', 1),
                    ],
                    'order_by' => ['re_properties.created_at' => 'DESC'],
                    'with' => RealEstateHelper::getPropertyRelationsQuery(),
                ];

                $properties = app(PropertyInterface::class)->getProperties($filters, $params);

                return [
                    'view' => 'real-estate.property-category',
                    'default_view' => 'plugins/real-estate::themes.property-category',
                    'data' => compact('category', 'properties'),
                    'slug' => $category->slug,
                ];

            case Account::class:
                abort_if(RealEstateHelper::isDisabledPublicProfile(), 404);

                $account = Account::query()
                    ->where([
                        'id' => $slug->reference_id,
                        'is_public_profile' => true,
                    ])
                    ->firstOrFail();

                SeoHelper::setTitle($account->name);

                Theme::breadcrumb()->add($account->name);

                if (function_exists('admin_bar')) {
                    admin_bar()->registerLink(__('Edit this agent'), route('account.edit', $account->getKey()));
                }

                $filters = [
                    'author_id' => $account->getKey(),
                ];


                $params = [
                    'paginate' => [
                        'per_page' => 12,
                        'current_paged' => $request->integer('page', 1),
                    ],
                    'order_by' => ['re_properties.created_at' => 'DESC'], // This orders properties by creation date in descending order (newest first)
                    'with' => RealEstateHelper::getPropertyRelationsQuery(),
                ];

                // $properties = app(PropertyInterface::class)->getProperties($filters, $params);
                $properties = $account->properties()->orderBy('created_at', 'DESC')->paginate(12);

                return [
                    'view' => 'real-estate.agent',
                    'default_view' => 'plugins/real-estate::themes.agent',
                    'data' => compact('account', 'properties'),
                    'slug' => $account->slug,
                ];

            case City::class:

                $city = City::query()
                    ->where('id', $slug->reference_id)
                    ->with(['slugable'])
                    ->firstOrFail();

                $city->loadMissing('metadata');

                $seo_meta = $city->getMetaData('seo_meta', true);

                SeoHelper::setTitle($seo_meta['seo_title'] ?? $city->name)
                    ->setDescription($seo_meta['seo_description'] ?? Str::words($city->description, 120));

                $meta = new SeoOpenGraph();
                if ($city->image) {
                    $meta->setImage(RvMedia::getImageUrl($city->image));
                }
                $meta->setDescription($city->description);
                $meta->setUrl($city->url);
                $meta->setTitle($city->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                SeoHelper::meta()->setUrl($city->url);

                $perPage = $request->integer('per_page') ?: (int) theme_option('number_of_properties_per_page', 12);
                $request->merge(['city_id' => $city->id, 'country_id' => $city->country->id]);

                if (theme_option('properties_list_page_id')) {
                    $propertiesListPage =  Page::query()
                        ->wherePublished()
                        ->where('id', theme_option('properties_list_page_id'))
                        ->select(['id', 'name'])
                        ->with(['slugable'])
                        ->first();
                    Theme::breadcrumb()->add($propertiesListPage->name, $propertiesListPage->url);
                }

                Theme::breadcrumb()->add($city->name);


                add_filter(THEME_FRONT_HEADER, function (?string $html) use ($city, $seo_meta): string|null {


                    $schema = [
                        '@context' => 'https://schema.org',
                        '@type' => 'CollectionPage',
                        'name' => $seo_meta['seo_title'] ?? $city->name,
                        'image' => [
                            RvMedia::getImageUrl($city->image, null, false, RvMedia::getDefaultImage()),
                        ],
                        'description' => $seo_meta['seo_description'] ?? Str::limit(strip_tags($city->description), 400),
                        'url' => $city->url,
                    ];


                    return $html . Html::tag('script', json_encode($schema, JSON_UNESCAPED_UNICODE), ['type' => 'application/ld+json'])
                        ->toHtml();
                }, 2);
                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, PROPERTY_MODULE_SCREEN_NAME, $city);

                $properties = RealEstateHelper::getPropertiesFilter($perPage, RealEstateHelper::getReviewExtraData());

                return [
                    'view' => 'real-estate.properties',
                    'default_view' => 'plugins/real-estate::themes.properties',
                    'data' => compact('city', 'properties'),
                    'slug' => $city->slug,
                ];
            case Country::class:
                $country = Country::query()
                    ->where('id', $slug->reference_id)
                    ->with(['slugable'])
                    ->firstOrFail();


                $country->loadMissing('metadata');

                $seo_meta = $country->getMetaData('seo_meta', true);

                SeoHelper::setTitle($seo_meta['seo_title'] ?? $country->name)
                    ->setDescription($seo_meta['seo_description'] ?? Str::words($country->description, 120));

                $meta = new SeoOpenGraph();
                if ($country->image) {
                    $meta->setImage(RvMedia::getImageUrl($country->image));
                }
                $meta->setDescription($country->description);
                $meta->setUrl($country->url);
                $meta->setTitle($country->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);

                SeoHelper::meta()->setUrl($country->url);

                $perPage = $request->integer('per_page') ?: (int) theme_option('number_of_properties_per_page', 12);
                $request->merge(['country_id' => $country->id]);

                if (theme_option('properties_list_page_id')) {
                    $propertiesListPage =  Page::query()
                        ->wherePublished()
                        ->where('id', theme_option('properties_list_page_id'))
                        ->select(['id', 'name'])
                        ->with(['slugable'])
                        ->first();
                    Theme::breadcrumb()->add($propertiesListPage->name, $propertiesListPage->url);
                }
                Theme::breadcrumb()->add($country->name);

                add_filter(THEME_FRONT_HEADER, function (?string $html) use ($country, $seo_meta): string|null {


                    $schema = [
                        '@context' => 'https://schema.org',
                        '@type' => 'CollectionPage',
                        'name' => $seo_meta['seo_title'] ?? $country->name,
                        'image' => [
                            RvMedia::getImageUrl(theme_option('logo')),
                        ],
                        'description' => $seo_meta['seo_description'] ?? Str::limit(strip_tags($country->description), 400),
                        'url' => $country->url,
                    ];


                    return $html . Html::tag('script', json_encode($schema, JSON_UNESCAPED_UNICODE), ['type' => 'application/ld+json'])
                        ->toHtml();
                }, 2);
                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, PROPERTY_MODULE_SCREEN_NAME, $country);

                $properties = RealEstateHelper::getPropertiesFilter($perPage, RealEstateHelper::getReviewExtraData());

                return [
                    'view' => 'real-estate.properties',
                    'default_view' => 'plugins/real-estate::themes.properties',
                    'data' => compact('country', 'properties'),
                    'slug' => $country->slug,
                ];
        }

        return $slug;
    }
}
