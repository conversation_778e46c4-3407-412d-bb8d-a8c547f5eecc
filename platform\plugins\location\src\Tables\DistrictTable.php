<?php

namespace Xmetr\Location\Tables;

use Xmetr\Base\Facades\Html;
use Xmetr\Location\Models\District;
use Xmetr\Table\Abstracts\TableAbstract;
use Xmetr\Table\Actions\DeleteAction;
use Xmetr\Table\Actions\EditAction;
use Xmetr\Table\BulkActions\DeleteBulkAction;
use Xmetr\Table\BulkChanges\CreatedAtBulkChange;
use Xmetr\Table\BulkChanges\NameBulkChange;
use Xmetr\Table\BulkChanges\StatusBulkChange;
use Xmetr\Table\Columns\Column;
use Xmetr\Table\Columns\CreatedAtColumn;
use Xmetr\Table\Columns\IdColumn;
use Xmetr\Table\Columns\NameColumn;
use Xmetr\Table\Columns\StatusColumn;
use Xmetr\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;
use Xmetr\Table\Columns\FormattedColumn;

class DistrictTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(District::class)
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('district.edit'),
                FormattedColumn::make('city_id')
                    ->title(trans('plugins/location::district.city'))
                    ->alignStart()
                    ->getValueUsing(function (FormattedColumn $column) {
                     $item = $column->getItem();
                        if (! $item->city->name) {
                            return '&mdash;';
                        }

                        return Html::link(route('city.edit', $item->city->id), $item->city->name);
                    }),
                FormattedColumn::make('state_id')
                    ->title(trans('plugins/location::district.state'))
                    ->alignStart()
                   ->getValueUsing(function (FormattedColumn $column) {
                     $item = $column->getItem();
                        if (! $item->state->name) {
                            return '&mdash;';
                        }

                        return Html::link(route('state.edit', $item->state->id), $item->state->name);
                    }),
                FormattedColumn::make('country_id')
                    ->title(trans('plugins/location::district.country'))
                    ->alignStart()
                    ->getValueUsing(function (FormattedColumn $column) {
                     $item = $column->getItem();
                        if (! $item->country->name) {
                            return '&mdash;';
                        }

                        return Html::link(route('country.edit', $item->country->id), $item->country->name);
                    }),
                FormattedColumn::make('average_rating')
                    ->title(trans('plugins/location::district.average_rating'))
                    ->alignCenter()
                    ->getValueUsing(function (FormattedColumn $column) {
                        $averageRating = $column->getItem()->average_rating;
                        if ($averageRating > 0) {
                            return '<span class="badge badge-info">' . number_format($averageRating, 2) . '/5</span>';
                        }
                        return '<span class="text-muted">&mdash;</span>';
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addHeaderAction(CreateHeaderAction::make()->route('district.create'))
            ->addActions([
                EditAction::make()->route('district.edit'),
                DeleteAction::make()->route('district.destroy'),
            ])
            ->addBulkAction(DeleteBulkAction::make()->permission('district.destroy'))
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'city_id',
                        'state_id',
                        'country_id',
                        'amenities_rating',
                        'transport_rating',
                        'safety_rating',
                        'green_spaces_rating',
                        'noise_rating',
                        'rent_rating',
                        'atmosphere_rating',
                        'created_at',
                        'status',
                    ])
                    ->with(['city', 'state', 'country']);
            });
    }

    public function getDefaultButtons(): array
    {
        return [
            'export',
            'reload',
        ];
    }


}
