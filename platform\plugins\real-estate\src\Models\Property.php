<?php

namespace Xmetr\RealEstate\Models;

use Xmetr\Base\Casts\SafeContent;
use Xmetr\Base\Models\BaseModel;
use Xmetr\Media\Facades\RvMedia;
use Xmetr\RealEstate\Enums\ModerationStatusEnum;
use Xmetr\RealEstate\Enums\PropertyPeriodEnum;
use Xmetr\RealEstate\Enums\PropertyStatusEnum;
use Xmetr\RealEstate\Enums\PropertyTypeEnum;
use Xmetr\RealEstate\Models\Traits\UniqueId;
use Xmetr\RealEstate\QueryBuilders\PropertyBuilder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Xmetr\RealEstate\Enums\RentalPeriodEnum;
use Xmetr\RealEstate\Enums\RequiredDocumentEnum;

/**
 * @method static \Xmetr\RealEstate\QueryBuilders\PropertyBuilder<static> query()
 */
class Property extends BaseModel
{
    use UniqueId;

    protected $table = 're_properties';

    protected $fillable = [
        'name',
        'type',
        'description',
        'original_description',
        'content',
        'location',
        'images',
        'project_id',
        'number_bedroom',
        'number_bathroom',
        'number_floor',
        'square',
        'price',
        'status',
        'is_featured',
        'currency_id',
        'city_id',
        'district_id',
        'state_id',
        'country_id',
        'period',
        'author_id',
        'author_type',
        'expire_date',
        'auto_renew',
        'bills_included',
        'utilities',
        'furnished',
        'pets_allowed',
        'smoking_allowed',
        'online_view_tour',
        'latitude',
        'longitude',
        'unique_id',
        'private_notes',
        'floor_plans',
        'reject_reason',
        'rental_period',
        'required_documents',
    ];

    protected $casts = [
        'status' => PropertyStatusEnum::class,
        'moderation_status' => ModerationStatusEnum::class,
        'type' => PropertyTypeEnum::class,
        'period' => PropertyPeriodEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'content' => SafeContent::class,
        'location' => SafeContent::class,
        'private_notes' => SafeContent::class,
        'expire_date' => 'datetime',
        'images' => 'json',
        'price' => 'float',
        'square' => 'float',
        'number_bedroom' => 'int',
        'number_bathroom' => 'int',
        'number_floor' => 'int',
        'bills_included' => 'boolean',
        'utilities' => 'float',
        'furnished' => 'boolean',
        'pets_allowed' => 'boolean',
        'smoking_allowed' => 'boolean',
        'online_view_tour' => 'boolean',
        'floor_plans' => 'array',
        'whatsapp_clicks' => 'int',
        'telegram_clicks' => 'int',
        'phone_clicks' => 'int',
        'rental_period' => RentalPeriodEnum::class,
        'required_documents' => 'array',
    ];

    protected static function booted(): void
    {
        static::deleting(function (Property $property): void {
            $property->categories()->detach();
            $property->customFields()->delete();
            $property->reviews()->delete();
            $property->features()->detach();
            $property->suitable()->detach();
            $property->facilities()->detach();
            $property->metadata()->delete();
        });
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class, 'project_id')->withDefault();
    }

    public function features(): BelongsToMany
    {
        return $this->belongsToMany(Feature::class, 're_property_features', 'property_id', 'feature_id');
    }

    public function suitable(): BelongsToMany
    {
        return $this->belongsToMany(Suitable::class, 're_property_suitable', 'property_id', 'suitable_id');
    }

    public function facilities(): BelongsToMany
    {
        return $this->morphToMany(Facility::class, 'reference', 're_facilities_distances')->withPivot('distance');
    }

    protected function image(): Attribute
    {
        return Attribute::get(fn () => Arr::first($this->images) ?? null);
    }

    protected function squareText(): Attribute
    {
        return Attribute::get(function () {
            $square = $this->square;

            $unit = setting('real_estate_square_unit', 'm²');

            return apply_filters('real_estate_property_square_text', sprintf('%s %s', number_format($square), __($unit)), $square);
        });
    }

    protected function address(): Attribute
    {
        return Attribute::get(fn () => $this->location);
    }

    protected function category(): Attribute
    {
        return Attribute::get(fn () => $this->categories->first() ?: new Category());
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    public function author(): MorphTo
    {
        return $this->morphTo()->withDefault();
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 're_property_categories');
    }

    protected function cityName(): Attribute
    {
        return Attribute::get(fn () => ($this->city->name ? $this->city->name . ', ' : null) . $this->state->name);
    }

    protected function typeHtml(): Attribute
    {
        return Attribute::get(fn () => $this->type->label());
    }

    protected function statusHtml(): Attribute
    {
        return Attribute::get(fn () => $this->status->toHtml());
    }

    protected function categoryName(): Attribute
    {
        return Attribute::get(fn () => $this->category->name);
    }

    protected function imageThumb(): Attribute
    {
        return Attribute::get(fn () => $this->image ? RvMedia::getImageUrl($this->image, 'thumb', false, RvMedia::getDefaultImage()) : null);
    }

    protected function imageSmall(): Attribute
    {
        return Attribute::get(fn () => $this->image ? RvMedia::getImageUrl($this->image, 'small', false, RvMedia::getDefaultImage()) : null);
    }

    protected function priceHtml(): Attribute
    {
        return Attribute::get(function () {
            if (! $this->price) {
                return __('Contact');
            }

            $price = $this->price_format;

            if ($this->type == PropertyTypeEnum::RENT) {
                $price .= ' / ' . Str::lower($this->period->label());
            }

            return $price;
        });
    }

    protected function priceFormat(): Attribute
    {
        return Attribute::get(function () {
            if (! $this->price) {
                return __('Contact');
            }

            if ($this->price_formatted) {
                return $this->price_formatted;
            }

            $currency = $this->currency;

            if (! $currency || ! $currency->getKey()) {
                $currency = get_application_currency();
            }

            return $this->price_formatted = format_price($this->price, $currency);
        });
    }

    protected function mapIcon(): Attribute
    {
        // return Attribute::get(fn () => $this->type_html . ': ' . $this->price_format);
        return Attribute::get(fn () => $this->price_format);
    }

    public function customFields(): MorphMany
    {
        return $this->morphMany(CustomFieldValue::class, 'reference', 'reference_type', 'reference_id')->with('customField.options');
    }

    protected function customFieldsArray(): Attribute
    {
        return Attribute::get(fn () => CustomFieldValue::getCustomFieldValuesArray($this));
    }

    public function reviews(): MorphMany
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    public function dailyViews(): HasMany
    {
        return $this->hasMany(PropertyDailyView::class, 'property_id');
    }

    public function contactClicks(): HasMany
    {
        return $this->hasMany(PropertyContactClick::class, 'property_id');
    }

    public function newEloquentBuilder($query): PropertyBuilder
    {
        return new PropertyBuilder($query);
    }

    protected function shortAddress(): Attribute
    {
        return Attribute::get(fn () => implode(', ', array_filter([$this->city->name, $this->state->name])));
    }

    protected function formattedFloorPlans(): Attribute
    {
        return Attribute::get(function () {
            return collect($this->floor_plans)
                ->filter(fn ($floorPlan) => is_array($floorPlan))
                ->map(function ($floorPlan) {
                    $floorPlan = collect($floorPlan)->pluck('value', 'key')->toArray();
                    $bedrooms = (int) Arr::get($floorPlan, 'bedrooms', 0);
                    $bathrooms = (int) Arr::get($floorPlan, 'bathrooms', 0);

                    return [
                        'name' => Arr::get($floorPlan, 'name'),
                        'description' => Arr::get($floorPlan, 'description'),
                        'image' => Arr::get($floorPlan, 'image'),
                        'bedrooms' => $bedrooms === 1 ? __('1 bedroom') : __(':count bedrooms', ['count' => $bedrooms]),
                        'bathrooms' => $bathrooms === 1 ? __('1 bathroom') : __(':count bathrooms', ['count' => $bathrooms]),
                    ];
                });
        });
    }

    protected function isPendingModeration(): Attribute
    {
        return Attribute::get(function () {
            if (! $this->exists) {
                return false;
            }

            return ! in_array($this->moderation_status, [ModerationStatusEnum::APPROVED, ModerationStatusEnum::REJECTED]);
        });
    }

    protected function timeAgoText(): Attribute
    {
        return Attribute::get(function () {
            $created = $this->created_at;

            if (!$created) {
                return null;
            }

            $now = Carbon::now();
            $diffInMinutes = $now->diffInMinutes($created);
            $diffInHours = $now->diffInHours($created);

            if ($diffInMinutes < 60) {
                return 'New';
            } elseif ($diffInHours < 24) {
                return $diffInHours . ' ' . Str::plural('hour', $diffInHours) . ' ago';
            } elseif ($diffInHours < 48) {
                return 'Yesterday';
            }

            return null;
        });
    }

    /**
     * Get today's views count for this property
     */
    public function getTodayViewsCount(): int
    {
        return PropertyDailyView::getViewsForDate($this->getKey());
    }

    /**
     * Get formatted views text with total and today's count
     */
    protected function viewsText(): Attribute
    {
        return Attribute::get(function () {
            $totalViews = $this->views ?: 0;
            $todayViews = $this->getTodayViewsCount();

            if ($totalViews === 0) {
                return __('No views');
            }

            if ($totalViews === 1) {
                $text = __('1 view');
            } else {
                $text = __(':number views', ['number' => number_format($totalViews)]);
            }

            if ($todayViews > 0) {
                if ($todayViews === 1) {
                    $text .= __(' (1 today)');
                } else {
                    $text .= __(' (:number today)', ['number' => number_format($todayViews)]);
                }
            }

            return $text;
        });
    }

    /**
     * Get today's click count for a specific contact type
     */
    public function getTodayClicksCount(string $contactType): int
    {
        return PropertyContactClick::getClicksForDate($this->getKey(), $contactType);
    }

    /**
     * Get formatted click text for a contact type with total and today's count
     */
    public function getClicksText(string $contactType): string
    {
        $totalClicks = $this->{$contactType . '_clicks'} ?: 0;
        $todayClicks = $this->getTodayClicksCount($contactType);

        if ($totalClicks === 0) {
            return __('No clicks');
        }

        if ($totalClicks === 1) {
            $text = __('1 click');
        } else {
            $text = __(':number clicks', ['number' => number_format($totalClicks)]);
        }

        if ($todayClicks > 0) {
            if ($todayClicks === 1) {
                $text .= __(' (1 today)');
            } else {
                $text .= __(' (:number today)', ['number' => number_format($todayClicks)]);
            }
        }

        return $text;
    }

        public function getRequiredDocumentsEnumAttribute()
        {
            if (empty($this->required_documents)) {
                return [];
            }

            if (!is_array($this->required_documents)) {
                $documents = json_decode($this->required_documents, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return [];
                }
            } else {
                $documents = $this->required_documents;
            }

            return collect($documents)
                ->filter()
                ->map(function ($item) {
                   return (new RequiredDocumentEnum())->make($item);
                })
                ->toArray();
        }

}


