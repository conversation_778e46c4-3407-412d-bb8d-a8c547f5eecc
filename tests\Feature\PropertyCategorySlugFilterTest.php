<?php

namespace Tests\Feature;

use Tests\TestCase;

class PropertyCategorySlugFilterTest extends TestCase
{
    public function test_helper_functions_exist()
    {
        // Test that our new helper functions exist
        $this->assertTrue(function_exists('get_property_category_id_by_slug'));
        $this->assertTrue(function_exists('get_property_categories_related_ids_by_slug'));
    }

    public function test_get_property_category_id_by_slug_returns_null_for_invalid_slug()
    {
        // Test with a non-existent slug
        $categoryId = get_property_category_id_by_slug('non-existent-slug');

        $this->assertNull($categoryId);
    }

    public function test_get_property_categories_related_ids_by_slug_returns_empty_for_invalid_slug()
    {
        // Test with a non-existent slug
        $categoryIds = get_property_categories_related_ids_by_slug('non-existent-slug');

        $this->assertIsArray($categoryIds);
        $this->assertEmpty($categoryIds);
    }
}
