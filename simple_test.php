<?php

// Simple test to check if our helper function works
echo "Testing category slug helper function...\n";

// Test if function exists
if (function_exists('get_property_category_id_by_slug')) {
    echo "✓ Function get_property_category_id_by_slug exists\n";
} else {
    echo "✗ Function get_property_category_id_by_slug does not exist\n";
    exit(1);
}

if (function_exists('get_property_categories_related_ids_by_slug')) {
    echo "✓ Function get_property_categories_related_ids_by_slug exists\n";
} else {
    echo "✗ Function get_property_categories_related_ids_by_slug does not exist\n";
    exit(1);
}

// Test with a simple slug
echo "\nTesting with slug 'apartment':\n";
$result = get_property_category_id_by_slug('apartment');
echo "Result: " . ($result ? "Category ID {$result}" : "NULL") . "\n";

echo "\nTesting with slug 'villa':\n";
$result = get_property_category_id_by_slug('villa');
echo "Result: " . ($result ? "Category ID {$result}" : "NULL") . "\n";

echo "\nTesting with slug 'condo':\n";
$result = get_property_category_id_by_slug('condo');
echo "Result: " . ($result ? "Category ID {$result}" : "NULL") . "\n";

echo "\nTesting with non-existent slug 'non-existent':\n";
$result = get_property_category_id_by_slug('non-existent');
echo "Result: " . ($result ? "Category ID {$result}" : "NULL") . "\n";

echo "\nTest completed!\n";
