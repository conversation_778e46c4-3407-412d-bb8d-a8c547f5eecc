<div class="login-options">
    <div class="login-options-title">
        <p><?php echo e(__('Login with social networks')); ?></p>
    </div>

    <?php if(setting('social_login_style', 'default') === 'basic'): ?>
        <ul class="social-login-basic">
            <?php $__currentLoopData = SocialService::getProviderKeys(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(! SocialService::getProviderEnabled($item)) continue; ?>

                <li>
                    <a href="<?php echo e(route('auth.social', array_merge([$item], $params))); ?>" class="social-login <?php echo e($item); ?>-login">
                        <?php
                            $item = $item === 'linkedin-openid' ? 'linkedin' : $item;
                        ?>

                        <img src="<?php echo e(asset('vendor/core/plugins/social-login/images/icons/logo-' . $item . '.svg')); ?>" alt="<?php echo e(Str::ucfirst($item)); ?>" />
                        <span><?php echo e(trans('plugins/social-login::social-login.sign_in_with', ['provider' => trans('plugins/social-login::social-login.socials.' . $item)])); ?></span>
                    </a>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php else: ?>
        <ul class="<?php echo \Illuminate\Support\Arr::toCssClasses(['social-icons', 'social-login-lg' => setting('social_login_style', 'default') === 'default']); ?>">
            <?php $__currentLoopData = SocialService::getProviderKeys(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(! SocialService::getProviderEnabled($item)) continue; ?>

                <?php echo apply_filters(
                    'social_login_' . $item . '_render',
                    view('plugins/social-login::social-login-item', ['social' => $item, 'url' => route('auth.social', isset($params) ? array_merge([$item], $params) : $item)])->render(),
                    $item
                ); ?>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
</div>
<?php /**PATH D:\laragon\www\xmetr\platform/plugins/social-login/resources/views/login-options.blade.php ENDPATH**/ ?>