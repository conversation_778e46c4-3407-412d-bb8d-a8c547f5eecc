<?php
    $itemLayout ??= request()->input('layout', 'grid');
    $itemLayout = in_array($itemLayout, ['grid', 'list']) ? $itemLayout : 'grid';
    $layout ??= get_property_listing_page_layout();

    if (! isset($itemsPerRow)) {
        $itemsPerRow = $itemLayout === 'grid' ? 3 : 2;
        if (! in_array($layout, ['top-map', 'without-map'])) {
            $itemsPerRow = $itemLayout === 'grid' ? 2 : 1;
        }
    }
?>

<?php if($properties->isNotEmpty()): ?>
<input type="hidden" id="found-filter-page-title" value="<?php if(isset($page_title)): ?> <?php echo e($page_title); ?> <?php endif; ?>">
<input type="hidden" id="found-listings" value="<?php echo e($properties instanceof \Illuminate\Pagination\LengthAwarePaginator ? $properties->total() : $properties->count()); ?>">
    <?php echo $__env->make(Theme::getThemeNamespace("views.real-estate.properties.$itemLayout"), compact('itemsPerRow'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php else: ?>
<?php
     SeoHelper::meta()->addMeta('robots', 'noindex, nofollow');
?>
    <input type="hidden" id="found-filter-page-title" value="<?php echo e(__('No properties found.')); ?>">
    <div class="alert alert-warning" role="alert" id="no-properties-found-filter-page-title">
        <?php echo e(__('No properties found.')); ?>

    </div>
<?php endif; ?>

<?php if($properties instanceof \Illuminate\Pagination\LengthAwarePaginator && $properties->hasPages()): ?>
    <div class="justify-content-center wd-navigation mt15">
       <?php
    $query = request()->except(['country_id', 'city_id']);
?>
        
       <?php echo e($properties->appends($query)->links(Theme::getThemeNamespace('partials.pagination'))); ?>

        
        

    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\xmetr\platform\themes/xmetr/views/real-estate/properties/index.blade.php ENDPATH**/ ?>